import { SharedStateConfig, sharedState } from "../shared-state";
import { CategoriesData, onCategoriesSnapshot } from "../../lib/categories";

//
// Loads vesselDocumentCategories
//

export const vesselDocumentCategoriesConfig: SharedStateConfig<CategoriesData> = {
    isAlwaysActive: false,
    dependencies: ['vesselId'],
    countLiveDocs: () => sharedState.vesselDocumentCategories.current?.ids.length ?? 0,
    run: (done, set, clear) => {
        clear();
        const vesselId = sharedState.vesselId.current;
        if (vesselId) {
            return onCategoriesSnapshot(
                'vesselDocumentCategories',
                'vesselId',
                vesselId,
                (data: CategoriesData) => { // onLoaded
                    done();
                    set(data);
                },
                (error) => { // onError
                    done();
                    console.log(`Error getting vesselDocumentCategories`, error);
                }
            );
        } else {
            done();
        }
    }
};