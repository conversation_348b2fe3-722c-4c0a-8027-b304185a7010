import { collection, onSnapshot, query } from "firebase/firestore";
import { firestore } from "../../lib/firebase";
import { UserType } from "../Core/user";
import { useEffect, useState } from "react";

//
// Loads all users.
// This can only be used by superAdmin.
//

export type AllUsersData = {
    byId: {
        [id: string]: UserType;
    }
}

export const useAllUsers = () => {
    const [allUsers, setAllUsers] = useState<AllUsersData>();

    useEffect(() => {
        setAllUsers(undefined);
        return onSnapshot(
            query(
                collection(firestore, 'users'),
                // where('state', '==', 'active')
            ),
            (snap) => {
                const byId = {} as any;

                snap.docs.forEach((doc) => {
                    byId[doc.id] = {
                        id: doc.id,
                        ...doc.data()
                    };
                });

                setAllUsers({
                    byId
                });
            },
            (error) => {
                console.log(`error getting allUsers`, error);
            }
        );
    }, []);

    return allUsers;
};
