/**
 * 
 * This file is repsonsible for saving and loading cachedFiles data to and from localStorage.
 * Currently, we are just dividing up the data into chunks and storing in window.storage.
 * 
 * If the number of FileCacheEntrys exceeds MAX_ENTRIES_PER_CHUNK, it will split into two chunks
 * 
 */
import { FileId } from "../../lib/files";
import { sharedState } from "../shared-state";
import { CachedFiles, FileCacheEntry, fileSyncProfiling, profileFileSync } from "./cachedFiles";

type Chunk = {
    [fileId: FileId]: FileCacheEntry
};
type StoredChunk = FileCacheEntry[]; // Chunks are stored as simple arrays to avoid the double up of fileIds

const idCharacters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'; // (base 62)
const MIN_CHUNK_ID = '00000000'; // No chunkId or fileId should be smaller than this
const MAX_CHUNK_ID = 'zzzzzzzz'; // No chunkId can be larger than this, although a fileId could be. No chunkId will ever equal this value, it's just used for calculating mid points.
const MAX_ENTRIES_PER_CHUNK = 512; // Once a chunk exceeds this number of entries it gets split in half

/**
 * Ordered array of chunkIds.
 * Each chunk with chunkId holds FileCacheEntrys with fileId >= chunkId but less than the next chunkId.
 */
let chunksLookup: string[] = []; 
let chunks = {} as {
    [chunkId: string]: Chunk
};
let chunkSizes = {} as {
    [chunkId: string]: number
};

/**
 * Loads cachedFiles by reconstituting chunks.
 * If old data is found we'll migrate into the new chunks structure.
 */
export const loadCachedFilesFromStore = () => {
    const startTime = performance.now();
    const chunksLookupValue = window.localStorage.getItem(`_${sharedState.licenseeId.current}_files_lookup`);
    if (chunksLookupValue) {
        // Load chunks
        const cachedFiles = {} as CachedFiles;
        let count = 0;
        chunksLookup = JSON.parse(chunksLookupValue);
        chunksLookup.forEach((chunkId) => {
            const chunk = {} as Chunk;
            chunks[chunkId] = chunk;
            chunkSizes[chunkId] = 0;
            const chunkValue = window.localStorage.getItem(`_${sharedState.licenseeId.current}_files_${chunkId}`);
            if (chunkValue) {
                const array = JSON.parse(chunkValue);
                chunkSizes[chunkId] = array.length;
                count += array.length;
                array.forEach((entry: FileCacheEntry) => {
                    chunk[entry[1]] = entry;
                    cachedFiles[entry[1]] = entry;
                });
            }
        });
        if (profileFileSync) {
            const took =  performance.now() - startTime;
            fileSyncProfiling.push({
                when: Date.now(),
                what: 'Load chunks from store',
                took: took,
                notes: `${count} entries loaded`
            });
            console.log(`[FileSync] Loaded ${count} file cache entries in ${took.toFixed(1)}ms.`);
        }
        return cachedFiles;
    }
    const legacyCachedFiles = window.localStorage.getItem(`_${sharedState.licenseeId.current}_cachedFiles`);
    if (legacyCachedFiles) {
        // Migrate from legacy structure (just cachedFiles stored in it's entirety) to new chunks structure
        const cachedFiles = JSON.parse(legacyCachedFiles);
        window.localStorage.removeItem(`_${sharedState.licenseeId.current}_cachedFiles`);
        saveCachedFilesToStore(cachedFiles);
        return cachedFiles;
    }
    // Start fresh new chunks structure
    chunksLookup = [MIN_CHUNK_ID];
    chunks = {
        [MIN_CHUNK_ID]: {}
    };
    chunkSizes = {
        [MIN_CHUNK_ID]: 0
    };
    saveChunksLookup();
    saveChunk(MIN_CHUNK_ID);
    return {} as CachedFiles;
};

/**
 * Repsonsible for saving new FileCacheEntry data to local storage.
 */
export const setFileCacheEntryStore = (fileId: FileId, entry: FileCacheEntry) => {
    const chunkIndex = getChunksLookupIndex(fileId);
    const chunkId = chunksLookup[chunkIndex];
    const chunk = chunks[chunkId];
    if (chunk[fileId]) {
        // Just updating
        chunk[fileId] = entry;
        saveChunk(chunkId);
    } else {
        // New entry
        chunk[fileId] = entry;
        chunkSizes[chunkId]++;
        if (chunkSizes[chunkId] > MAX_ENTRIES_PER_CHUNK) {
            saveChunk(
                splitChunk(chunkIndex, chunkId, chunk)
            );
            saveChunksLookup();
        }
        saveChunk(chunkId);
    }
};

/**
 * Responsible for deleting a FileCacheEntry from local storage.
 */
export const deleteFileCacheEntryStore = (fileId: FileId) => {
    const chunkId = chunksLookup[
        getChunksLookupIndex(fileId)
    ];
    if (chunks[chunkId][fileId]) {
        delete chunks[chunkId][fileId];
        chunkSizes[chunkId]--;
        saveChunk(chunkId);
    }
};

/**
 * Saves chunksLookup to local storage.
 */
const saveChunksLookup = () => {
    window.localStorage.setItem(
        `_${sharedState.licenseeId.current}_files_lookup`,
        JSON.stringify(chunksLookup)
    );
};
//let saveCount = 0;
//const saveCounts = {} as any; // For debugging only
const alreadySaving = {} as { [chunkId: string]: boolean }; // To help prevent saveChunk being spammed
/**
 * Save a particular to local storage.
 */
const saveChunk = (chunkId: string) => {
    if (alreadySaving[chunkId]) {
        return;
    }
    alreadySaving[chunkId] = true;
    setTimeout(() => {
        window.localStorage.setItem(
            `_${sharedState.licenseeId.current}_files_${chunkId}`,
            JSON.stringify(Object.values(chunks[chunkId]) as StoredChunk)
        );
        alreadySaving[chunkId] = false;
        //if (saveCounts[chunkId] === undefined) {
        //    saveCounts[chunkId] = 0;
        //}
        //saveCounts[chunkId]++;
        //saveCount++;
        //console.log(`[FileSync] Saved to chunk ${chunkId} [${saveCounts[chunkId]}] total=${saveCount}`);
    }, 500);
};

/**
 * Splits a chunk in half.
 * A new chunk will be created with a chunkId that is lexicographically midway 
 * between the specified chunkId and the next chunkId.
 */
const splitChunk = (chunkIndex: number, chunkId: string, chunk: Chunk) => {
    const nextChunkId = (chunkIndex + 1) < chunksLookup.length ? chunksLookup[chunkIndex + 1] : MAX_CHUNK_ID;
    // Find new midpoint chunksLookup id
    const newChunkId = calculateIdMidPoint(chunkId, nextChunkId);
    // Create new chunk (Map)
    const newChunk = {} as Chunk;
    // Insert into chunksLookup
    chunksLookup.splice(chunkIndex + 1, 0, newChunkId);
    chunks[newChunkId] = newChunk;
    chunkSizes[newChunkId] = 0;
    // Move bottom half of original chunk into newChunk
    Object.values(chunk).forEach((entry) => {
        const id = entry[1];
        if (id >= newChunkId) {
            newChunk[id] = entry;
            delete chunk[id];
            chunkSizes[chunkId]--;
            chunkSizes[newChunkId]++;
        }
    });
    return newChunkId;
};

/**
 * Returns the correct chunksLookup index given a FileId
 */
const getChunksLookupIndex = (fileId: FileId) => {
    let indexLow = 0;
    let indexHigh = chunksLookup.length;
    while (indexHigh - indexLow > 1) {
        const indexMid = Math.floor((indexLow + indexHigh) / 2);
        if (fileId >= chunksLookup[indexMid]) {
            indexLow = indexMid;
        } else {
            indexHigh = indexMid;
        }
    }
    //console.log(`found indexLow=${indexLow} ${chunksLookup[indexLow]}-${indexHigh === chunksLookup.length ? 'END' : chunksLookup[indexHigh]} id=${fileId}`);
    return indexLow;
    // Old linear approach
    // let index = 0;
    // for (; index < chunksLookup.length; index++) {
    //     if (fileId < chunksLookup[index]) {
    //         break;
    //     }
    // }
    // return index - 1;
};

/**
 * Saves the entrie cachedFiles database to local storage.
 * This should only be used where necessary as it is costly.
 */
export const saveCachedFilesToStore = (cachedFiles: CachedFiles) => {
    chunksLookup = [MIN_CHUNK_ID];
    chunks = {
        [MIN_CHUNK_ID]: {}
    };
    chunkSizes = {
        [MIN_CHUNK_ID]: 0
    };

    Object.keys(cachedFiles).forEach((fileId) => {
        if (fileId.length !== 20) {
            console.log(`[FileSync] fileId weird ${fileId}!`, cachedFiles[fileId]);
            return;
        }
        // Insert into the appropraite chunk
        const index = getChunksLookupIndex(fileId);
        //console.log(`chunksLookup[${index}]=${chunksLookup[index]}`);
        const chunkId = chunksLookup[index];
        const chunk = chunks[chunkId];
        chunk[fileId] = cachedFiles[fileId];
        chunkSizes[chunkId]++;

        if (chunkSizes[chunkId] > MAX_ENTRIES_PER_CHUNK) {
            splitChunk(index, chunkId, chunk);
        }
    });

    // Save chunks and lookup
    chunksLookup.forEach((chunkId) => {
        saveChunk(chunkId);
    });
    saveChunksLookup();
}




/**
 * Convert a string of idCharacters into an equivalent bigint
 */
const idToBigint = (id: string) => {
    let big = BigInt(0);
    let placeMult = BigInt(1);
    for (let i = 0; i < id.length; i++) {
        const c = id.charAt(id.length - i - 1); // Get character going from right to left
        if (i > 0) {
            placeMult *= BigInt(62);
        }
        big += placeMult * BigInt(idCharacters.indexOf(c));
    }
    return big;
};
/**
 * Convert a bigint into a string of idCharacters (base62)
 */
const bigintToId = (big: bigint, numPlaces: number) => {
    let s = '';
    for (let i = 0; i < numPlaces; i++) {
        let placeMult = BigInt(1);
        for (let j = 1; j < numPlaces - i; j++) {
            placeMult *= BigInt(62);
        }
        const placeValue = big / placeMult;
        big -= placeValue * placeMult;
        s += idCharacters[Number(placeValue)];
    }
    return s;
};

/**
 * Find the mid point between to strings that consist of idCharacters (lexicographically)
 */
const calculateIdMidPoint = (id1: string, id2: string) => {
    const aBig = idToBigint(id1);
    const bBig = idToBigint(id2);
    return bigintToId(
        aBig + ((bBig - aBig) / BigInt(2)),
        Math.max(id1.length, id2.length)
    );
};
