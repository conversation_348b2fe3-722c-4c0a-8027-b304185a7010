import { useState, useEffect } from 'react';
import { CategoriesData,  onCategoriesSnapshot } from '../../lib/categories';
import { sharedState } from '../shared-state';

/**
 * Returns vesselSystems for the selectedVesselId.
 * If selectedVesselId matches sharedState.vesselId we can just use sharedState.vesselSystems
 */
export const useVesselSystems = (selectedVesselId: string | undefined) => {
    const isActive = selectedVesselId ? true : false;
    const vesselId = sharedState.vesselId.use(isActive);
    const vesselSystems = sharedState.vesselSystems.use(isActive);
    const [otherVesselSystems, setOtherVesselSystems] = useState<CategoriesData>();

    useEffect(() => {
        setOtherVesselSystems(undefined);
        if (
            selectedVesselId && (
                vesselId === undefined ||
                vesselId !== selectedVesselId
            )
        ) {
            // We need to load vesselSystems for a vesselId other than sharedState.vesselId
            return onCategoriesSnapshot(
                'vesselSystems',
                'vesselId',
                selectedVesselId,
                (data) => { // onLoaded
                    setOtherVesselSystems(data);
                },
                (error) => { // onError
                    console.log(`Error getting vesselSystems for vesselId=${selectedVesselId}`, error);
                }
            );
        }
    }, [selectedVesselId, vesselId]);

    if (vesselId && vesselId === selectedVesselId) {
        // We have already loaded via sharedState.vesselSystems
        return vesselSystems;
    }
    return otherVesselSystems;
};
