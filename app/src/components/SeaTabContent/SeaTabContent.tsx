import React, { ReactNode } from 'react';
import './SeaTabContent.css';

interface SeaTabContentProps {
    children?: ReactNode,
    tab: string,
    selectedTab?: string,
    className?: string
}

const SeaTabContent: React.FC<SeaTabContentProps> = ({ children, tab, selectedTab, className }) => {
    return (
        // <div>
        //     {(tab === selectedTab) && children}
        // </div>
        // I prefer the following so every tab page doesn't need to reload data AND it's less jerky...
        <div className={className} style={{ display: (tab === selectedTab) ? 'block' : 'none' }}>
            {children}
        </div>
    );
};

export default SeaTabContent;
