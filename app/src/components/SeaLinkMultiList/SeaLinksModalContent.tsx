import React, { use<PERSON>allback, useMemo } from 'react';
import { IonCol, IonGrid, IonItem, IonRow, IonSpinner, isPlatform } from '@ionic/react';
import { LinkCollectionOption, LinkType } from '../../shared-state/General/links';
import { LinkSide } from '../SeaLinks/SeaLinks';
import { renderLinkLabelFromCollection, renderLinkName } from '../../lib/links';
import { sharedState } from '../../shared-state/shared-state';
import { LinkItem, LinkOptionsType } from './SeaLinkMultiList';
import { renderVesselName } from '../../shared-state/Core/vessels';
import { useSafetyChecksByCategory } from '../../shared-state/VesselSafety/useSafetyChecksByCategory';
import { useMaintenanceTasksBySystem } from '../../shared-state/VesselMaintenance/useMaintenanceTasksBySystem';
import { useVesselSOPsByCategory } from '../../shared-state/VesselDocuments/useVesselSOPsByCategory';
import { useVesselDocumentsByCategory } from '../../shared-state/VesselDocuments/useVesselDocumentsByCategory';
import { useVesselDrills } from '../../shared-state/VesselSafety/useVesselDrills';
import SeaInput from '../SeaInput/SeaInput';
import SeaButton from '../SeaButton/SeaButton';
import SeaIcon from '../SeaIcon/SeaIcon';
import SeaTabsGroup from '../SeaTabsGroup/SeaTabsGroup';
import SeaTab from '../SeaTab/SeaTab';
import SeaTabContent from '../SeaTabContent/SeaTabContent';
import SeaSearchbar from '../SeaSearchbar/SeaSearchbar';
import SeaNoData from '../SeaNoData/SeaNoData';
import SeaSelectVessel from '../SeaSelectVessel/SeaSelectVessel';
import './SeaLinkMultiList.css';

interface SeaLinksModalContentProps {
    values: LinkType[];
    title?: string;
    currentTab: LinkCollectionOption;
    setCurrentTab: React.Dispatch<React.SetStateAction<LinkCollectionOption>>;
    searchText: string,
    setSearchText: React.Dispatch<React.SetStateAction<string>>,
    filterVesselId: string | undefined,
    setFilterVesselId: React.Dispatch<React.SetStateAction<string | undefined>>,
    linkCollectionOptions?: LinkCollectionOption[];
    onClick?: (option: LinkItem, optionGroup: LinkOptionsType) => void;
    externalLinks: LinkSide[];
    addExternalLink: () => void;
    changeExternalLink: (e: React.ChangeEvent<HTMLInputElement>, link: LinkSide, index: number) => void;
    removeExternalLink: (index: number) => void;
    allowCustom?: boolean;
    handleDone: () => void;
    vesselIds?: string[];
    showVesselNameInCategory?: boolean;
}

const SeaLinksModalContent: React.FC<SeaLinksModalContentProps> = ({
    values,
    title = 'Links',
    currentTab,
    setCurrentTab,
    searchText,
    setSearchText,
    filterVesselId,
    setFilterVesselId,
    linkCollectionOptions,
    onClick,
    externalLinks,
    addExternalLink,
    changeExternalLink,
    removeExternalLink,
    handleDone,
    vesselIds,
    showVesselNameInCategory,
}) => {
    const drills = useVesselDrills(currentTab === 'drills', filterVesselId);
    const deviceInfo = sharedState.deviceInfo.use();
    const risks = sharedState.risks.use(currentTab === 'risks');
    const riskCategories = sharedState.riskCategories.use(currentTab === 'risks');
    const trainingTasks = sharedState.trainingTasks.use(currentTab === 'trainingTasks');
    const customForms = sharedState.customForms.use(currentTab === 'customForms');
    const customFormCategories = sharedState.customFormCategories.use(currentTab === 'customForms');
    const vesselSOPs = useVesselSOPsByCategory(currentTab === 'SOPs', filterVesselId);
    const companyDocuments = sharedState.companyDocuments.use(currentTab === 'companyDocuments');
    const companyDocumentCategories = sharedState.companyDocumentCategories.use(currentTab === 'companyDocuments');
    const vesselDocuments = useVesselDocumentsByCategory(currentTab === 'vesselDocuments', filterVesselId);
    const scheduledMaintenanceTasks = useMaintenanceTasksBySystem(currentTab === 'scheduledMaintenanceTasks', filterVesselId);
    const safetyChecks = useSafetyChecksByCategory(currentTab === 'safetyCheckItems', filterVesselId);

    const renderLinkCategoryName = useCallback(
        (name: string, vesselName?: string) => {
            return `${showVesselNameInCategory && vesselName ? vesselName + ' - ' : ''}${name}`;
        },
        [showVesselNameInCategory]
    );







    const externalLinkOptions = useMemo(() => {
        if (currentTab === 'external') {
            return {
                collection: 'external',
                label: 'External Links',
                items: externalLinks.map((link) => ({
                    id: link.id,
                    name: link.linkId,
                    vesselId: 'na',
                })),
            } as LinkOptionsType;
        }
        return undefined;
    }, [currentTab, externalLinks]);

    const scheduledMaintenanceTaskOptions = useMemo(() => {
        if (currentTab === 'scheduledMaintenanceTasks' && scheduledMaintenanceTasks) {
            return {
                collection: 'scheduledMaintenanceTasks',
                label: 'Maintenance Tasks',
                categories: scheduledMaintenanceTasks.systems.map((system) => {
                    return {
                        ...system,
                        items: scheduledMaintenanceTasks.bySystemId[system.id].map((item) => {
                            return {
                                id: item.id,
                                name: renderLinkName(item.task || 'placeholder task', item.state),
                                vesselId: item.vesselId ?? 'na'
                            };
                        })
                    }
                })
                // Review: Code below seems like it'll never be needed
                // items: Object.values(scheduledMaintenanceTasks.byId).map((item) => ({
                //     id: item.id,
                //     name: renderLinkName(item.task || 'placeholder task', item.state),
                //     vesselId: item.vesselId ?? 'na',
                // })),
            } as LinkOptionsType;
        }
        return undefined;
    }, [currentTab, scheduledMaintenanceTasks]);

    const drillOptions = useMemo(() => {
        if (currentTab === 'drills' && drills) {
            return {
                collection: 'drills',
                label: 'Drills',
                items: drills.map((item) => ({
                    id: item.id,
                    name: renderLinkName(item.name || 'placeholder drill', item.state),
                    vesselId: item.vesselId ?? 'na',
                }))
            } as LinkOptionsType;
        }
    }, [currentTab, drills]);

    const riskOptions = useMemo(() => {
        if (currentTab === 'risks' && risks && riskCategories) {
            return {
                collection: 'risks',
                label: 'Risk Assessments',
                categories: Object.keys(risks.byCategoryId).map((id) => ({
                    id,
                    name: renderLinkCategoryName(riskCategories.byId[id].name),
                    items: risks.byCategoryId[id].map((risk) => ({
                        id: risk.id,
                        name: renderLinkName(risk.name, risk.state),
                        vesselId: 'na',
                    })),
                })),
                // Review: Code below seems like it'll never be neede
                // items: Object.values(risks.byId).map((item) => ({
                //     id: item.id,
                //     name: renderLinkName(item.name, item.state),
                //     vesselId: 'na',
                // })),
            } as LinkOptionsType;
        }
        return undefined;
    }, [currentTab, renderLinkCategoryName, riskCategories, risks]);

    const trainingTaskOptions = useMemo(() => {
        if (currentTab === 'trainingTasks' && trainingTasks) {
            if (vesselIds?.length === 1) {
                const tt = trainingTasks?.allByVesselId[vesselIds[0]] ?? [];

                return {
                    collection: 'trainingTasks',
                    label: 'Crew Training',
                    items: tt.map((item) => ({
                        id: item.id,
                        name: renderLinkName(item.task || 'placeholder training', item.state),
                        vesselId: item.vesselId ?? 'na',
                    })),
                } as LinkOptionsType;
            }
            return {
                collection: 'trainingTasks',
                label: 'Crew Training',
                items: Object.values(trainingTasks.byId || {}).map((item) => ({
                    id: item.id,
                    name: renderLinkName(item.task || 'placeholder training', item.state),
                    vesselId: item.vesselId ?? 'na',
                })),
                categories: Object.entries(trainingTasks.allByVesselId)
                    .filter(([categoryId]) => vesselIds?.includes(categoryId))
                    .map(([categoryId, category]) => ({
                        id: categoryId,
                        name: renderVesselName(categoryId),
                        items: category.map((item) => ({
                            id: item.id,
                            name: renderLinkName(item.task || 'placeholder training', item.state),
                            vesselId: item.vesselId ?? 'na',
                        })),
                    })),
            } as LinkOptionsType;
        }
        return undefined;
    }, [currentTab, trainingTasks, vesselIds]);

    const customFormOptions = useMemo(() => {
        if (currentTab === 'customForms' && customForms && customFormCategories) {
            return {
                collection: 'customForms',
                label: 'Forms/Checklists',
                categories: Object.keys(customForms.byCategoryId).map((id) => ({
                    id,
                    name: renderLinkCategoryName(customFormCategories.byId[id].name),
                    items: customForms.byCategoryId[id].map((task) => ({
                        id: task.id,
                        name: renderLinkName(task.title, task.state),
                        vesselId: 'na',
                    })),
                })),
                // Review: Code below seems like it'll never be neede
                // items: Object.values(customForms.byId).map((item) => ({
                //     id: item.id,
                //     name: renderLinkName(item.title, item.state),
                //     vesselId: 'na',
                // })),
            } as LinkOptionsType;
        }
        return undefined;
    }, [currentTab, customFormCategories, customForms, renderLinkCategoryName]);

    const sopOptions = useMemo(() => {
        if (currentTab === 'SOPs' && vesselSOPs) {
            return {
                collection: 'SOPs',
                label: 'Standard Operating Procedures',
                categories: vesselSOPs.categories.map((category) => ({
                    ...category,
                    items: vesselSOPs.byCategoryId[category.id].map((item) => ({
                        id: item.id,
                        name: renderLinkName(item.title || '-', item.state),
                        vesselId: item.vesselId ?? 'na',
                    }))
                }))
                // Review: Code below seems like it'll never be needed
                // items: Object.values(licenseeSOPs.byId).map((item) => ({
                //     id: item.id,
                //     name: renderLinkName(item.title, item.state),
                //     vesselId: item.vesselId ?? 'na',
                // })),
            } as LinkOptionsType;
        }
        return undefined;
    }, [currentTab, vesselSOPs]);

    const companyDocumentOptions = useMemo(() => {
        if (currentTab === 'companyDocuments' && companyDocuments && companyDocumentCategories) {
            return {
                collection: 'companyDocuments',
                label: 'Company Documents',
                categories: Object.keys(companyDocuments.byCategoryId).map((id) => ({
                    id: id,
                    name: renderLinkCategoryName(companyDocumentCategories.byId[id].name),
                    items: companyDocuments.byCategoryId[id].map((doc) => ({
                        id: doc.id,
                        name: renderLinkName(doc.title, doc.state),
                        vesselId: 'na',
                    })),
                })),
                // Review: Code below seems like it'll never be needed
                // items: Object.values(companyDocuments.byId).map((item) => ({
                //     id: item.id,
                //     name: renderLinkName(item.title, item.state),
                //     vesselId: 'na',
                // })),
            } as LinkOptionsType;
        }
        return undefined;
    }, [companyDocumentCategories, companyDocuments, currentTab, renderLinkCategoryName]);

    const vesselDocumentOptions = useMemo(() => {
        if (currentTab === 'vesselDocuments' && vesselDocuments) {
            return {
                collection: 'vesselDocuments',
                label: 'Vessel Documents',
                categories: vesselDocuments.categories.map((category) => ({
                    ...category,
                    items: vesselDocuments.byCategoryId[category.id].map((item) => ({
                        id: item.id,
                        name: renderLinkName(item.title || '-', item.state),
                        vesselId: item.vesselId ?? 'na',
                    }))
                }))
                // Review: Code below seems like it'll never be needed
                // items: Object.values(licenseeVesselDocuments.byId).map((item) => ({
                //     id: item.id,
                //     name: renderLinkName(item.title, item.state),
                //     vesselId: item.vesselId,
                // })),
            } as LinkOptionsType;
        }
        return undefined;
    }, [currentTab, vesselDocuments]);

    const safetyCheckItemOptions = useMemo(() => {
        if (currentTab === 'safetyCheckItems' && safetyChecks) {
            return {
                collection: 'safetyCheckItems',
                label: 'Safety Checks',
                categories: safetyChecks.categories.map((category) => ({
                    ...category,
                    items: safetyChecks.byCategoryId[category.id].map((item) => ({
                        id: item.id,
                        name: renderLinkName(item.name || '-', item.state),
                        vesselId: item.vesselId ?? 'na',
                    }))
                }))
                // Review: Code below seems like it'll never be needed
                // items: safetyChecks.all.map((item) => ({
                //     id: item.id,
                //     name: renderLinkName(item.name || '-', item.state),
                //     vesselId: item.vesselId ?? 'na',
                // })),
            } as LinkOptionsType;
        }
        return undefined;
    }, [currentTab, safetyChecks]);

    const linkOptions = useMemo(() => {
        switch (currentTab) {
            case 'external':
                return externalLinkOptions;
            case 'scheduledMaintenanceTasks':
                return scheduledMaintenanceTaskOptions;
            case 'drills':
                return drillOptions;
            case 'risks':
                return riskOptions;
            case 'trainingTasks':
                return trainingTaskOptions;
            case 'customForms':
                return customFormOptions;
            case 'SOPs':
                return sopOptions;
            case 'companyDocuments':
                return companyDocumentOptions;
            case 'vesselDocuments':
                return vesselDocumentOptions;
            case 'safetyCheckItems':
                return safetyCheckItemOptions;
            default:
                return undefined;
        }
    }, [companyDocumentOptions, currentTab, customFormOptions, drillOptions, externalLinkOptions, riskOptions, safetyCheckItemOptions, scheduledMaintenanceTaskOptions, sopOptions, trainingTaskOptions, vesselDocumentOptions]);

    // Filter using searchText
    const filteredLinkOptions = useMemo(() => {
        const text = (searchText ?? '').toLowerCase().trim();
        if (text === '') {
            return linkOptions;
        }
        const result = { ...linkOptions };
        const isMatch = (o: any) => (o.name && o.name.toLowerCase().indexOf(text) !== -1);
        if (result?.categories) {
            const newCategories = [] as {
                id: string;
                name: string;
                items: LinkItem[];
            }[];
            result.categories.forEach((category) => {
                if (isMatch(category)) {
                    newCategories.push(category);
                    return;
                }
                const newItems = category.items.filter(isMatch);
                if (newItems.length > 0) {
                    newCategories.push({
                        ...category,
                        items: newItems
                    });
                }
            });
            result.categories = newCategories;
        } else if (result?.items) {
            result.items = result.items.filter(isMatch);
        }
        return result as LinkOptionsType;
    }, [linkOptions, searchText]);

    const hasNoResults = useMemo(() => {
        if (filterVesselId === '') return true;
        if (!filteredLinkOptions) return false;
        if (!filteredLinkOptions.categories && filteredLinkOptions.items && filteredLinkOptions.items.length === 0) return true;
        if (filteredLinkOptions.categories && filteredLinkOptions.categories.every((category) => category.items.length === 0)) return true;
        return false;
    }, [filterVesselId, filteredLinkOptions]);

    return (
        <>
            <div className="links-title-container">
                {title && (
                    <div className="links-title">
                        <h5>{title}</h5>
                    </div>
                )}
            </div>
            <div className="sea-select-multi-links">
                <SeaTabsGroup selectedTab={currentTab} setTab={(tab) => setCurrentTab(tab as LinkCollectionOption)} mode="forms" mini={true} allowFades={false}>
                    {linkCollectionOptions?.map((option) => (
                        <SeaTab key={option} tab={option} mode="forms">
                            {renderLinkLabelFromCollection(option, false, true)}
                        </SeaTab>
                    ))}
                </SeaTabsGroup>
                {isPlatform('tablet') && deviceInfo?.platform === 'web' && (
                    <div
                        style={{
                            position: 'absolute',
                            right: '0',
                            top: '50%',
                            transform: 'translateY(-50%)',
                        }}
                    >
                        {`<`}
                    </div>
                )}
            </div>
            {currentTab !== 'external' && (
                <div className="columns links-filters">
                    <div>
                        <SeaSearchbar value={searchText} setValue={setSearchText} />
                    </div>
                    {(
                        currentTab === 'safetyCheckItems' ||
                        currentTab === 'scheduledMaintenanceTasks' ||
                        currentTab === 'SOPs' ||
                        currentTab === 'drills' ||
                        currentTab === 'vesselDocuments'
                    ) &&
                        <div style={{ paddingLeft: '8px' }}>
                            <SeaSelectVessel
                                width="200px"
                                emptyText="None"
                                vesselId={filterVesselId}
                                setVesselId={setFilterVesselId}
                                allowNone={false}
                            />
                        </div>
                    }
                </div>
            )}
            <div className="sea-tab-content-container">
                <div className="no-data-container">
                    <SeaNoData dataName={filteredLinkOptions?.label || 'links'} hasNoData={hasNoResults && currentTab !== 'external'} isUsingFilter={true} />
                </div>
                {filteredLinkOptions ? (
                    <SeaTabContent
                        key={filteredLinkOptions.collection + '-tab-content'}
                        tab={filteredLinkOptions.collection!}
                        selectedTab={currentTab}
                        className={`sea-tab-content ${filteredLinkOptions.collection === 'external' ? 'external-links' : ''}`}
                    >
                        {currentTab === 'external' ? (
                            <IonGrid className="form-grid">
                                {externalLinks.map((link, index) => (
                                    <IonRow className="input-list-row" key={link.id + index}>
                                        <IonCol size="12" style={{ display: 'flex', flexGrow: 1, flexDirection: 'row' }}>
                                            <SeaInput zone="white" value={link.linkId} onblur={(e) => changeExternalLink(e as any, link, index)} />
                                            <div>
                                                <div className="trash" onClick={() => removeExternalLink(index)}>
                                                    <SeaIcon slot="icon-only" icon="trash" />
                                                </div>
                                            </div>
                                        </IonCol>
                                    </IonRow>
                                ))}
                                <div className="sea-add-new-button">
                                    <SeaButton zone="white" shape="circle" onClick={addExternalLink}>
                                        <SeaIcon slot="icon-only" icon="add" />
                                    </SeaButton>
                                    <div className="text" onClick={addExternalLink}>
                                        Add External Link
                                    </div>
                                </div>
                            </IonGrid>
                        ) : filteredLinkOptions.categories ? (
                            filteredLinkOptions.categories?.map((category) => {
                                if (category.items.length === 0) {
                                    return null;
                                }
                                return (
                                    <React.Fragment key={category.id}>
                                        <div className="category-heading">{category.name}</div>
                                        {category.items.map((item, index) => (
                                            <IonItem key={item.id + index} button={true} onClick={() => onClick?.(item, filteredLinkOptions)} lines={index === category.items.length - 1 ? 'none' : 'full'}>
                                                {item.name}
                                                {values.some((value) => value.aId === item.id || value.bId === item.id) && <SeaIcon slot="end" icon="tick" />}
                                            </IonItem>
                                        ))}
                                    </React.Fragment>
                                );
                            })
                        ) : (
                            filteredLinkOptions.items?.map((item) => (
                                <IonItem key={item.id} button={true} onClick={() => onClick?.(item, filteredLinkOptions)}>
                                    {item.name}
                                    {values.some((value) => value.aId === item.id || value.bId === item.id) && <SeaIcon slot="end" icon="tick" />}
                                </IonItem>
                            ))
                        )}
                    </SeaTabContent>
                ) : (
                    hasNoResults ? <></> : <IonSpinner name="crescent" className="sea-spinner" />
                )}
            </div>
            <div className="done-button-container">
                <SeaButton onClick={handleDone}>Done</SeaButton>
            </div>
        </>
    );
};

export default SeaLinksModalContent;
