const webpack = require('webpack');

module.exports = function override(config) {
    config.resolve.fallback = {
        ...config.resolve.fallback,
        stream: require.resolve("stream-browserify"),
        zlib: require.resolve("browserify-zlib"),
        util: require.resolve("util/"),
        process: require.resolve("process/browser"),
        assert: require.resolve("assert/"),
        buffer: require.resolve("buffer/"),
    };
    config.plugins = [
        ...config.plugins,
        new webpack.ProvidePlugin({
            process: 'process/browser',
            Buffer: ['buffer', 'Buffer']
        })
    ];

    config.module = {
        ...config.module,
        rules: [
            ...(config.module?.rules || []),
            {
                test: /\.(js|ts)$/,
                enforce: 'pre',
                use: ['source-map-loader'],
                exclude: /[\\/]node_modules[\\/]capacitor-ios-autofill-save-password[\\/]/
            }
        ]
    };

    if (config.devServer) {
        config.devServer.setupMiddlewares = (middlewares, devServer) => {
            if (!devServer) {
                throw new Error('webpack-dev-server is not defined');
            }
            return middlewares;
        };
    }

    return config;
};