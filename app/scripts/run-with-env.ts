import { spawn } from 'child_process';
import dotenv from 'dotenv';

const env = process.argv[2];
const command = process.argv.slice(3).join(' ');

if (!env) {
    console.error('Please specify an environment (dev, staging, or prod)');
    process.exit(1);
}

// Load the appropriate .env file
dotenv.config({ path: `.env.${env}` });

// Run the command with the loaded environment variables
const child = spawn(command, { shell: true, stdio: 'inherit', env: { ...process.env } });

child.on('exit', (code) => {
    process.exit(code || 0);
});