{"name": "sea-flux", "version": "1.5.21", "build": "1008", "private": true, "dependencies": {"@awesome-cordova-plugins/core": "^6.8.0", "@babel/runtime": "^7.15.4", "@capacitor-community/file-opener": "^6.0.0", "@capacitor/android": "^6.1.1", "@capacitor/app": "6.0.0", "@capacitor/camera": "6.0.1", "@capacitor/cli": "^6.1.1", "@capacitor/core": "^6.1.1", "@capacitor/device": "6.0.0", "@capacitor/filesystem": "6.0.0", "@capacitor/geolocation": "6.0.0", "@capacitor/ios": "^6.1.1", "@capacitor/share": "^6.0.1", "@dnd-kit/core": "^6.3.1", "@ionic/core": "^6.0.0", "@ionic/react": "^6.7.5", "@lexical/clipboard": "^0.11.1", "@lexical/html": "^0.11.1", "@lexical/link": "^0.11.1", "@lexical/list": "^0.11.1", "@lexical/react": "^0.11.1", "@lexical/rich-text": "^0.11.1", "@lexical/selection": "^0.11.1", "@lexical/table": "^0.11.1", "@lexical/utils": "^0.11.1", "@react-pdf/png-js": "^2.0.0", "@react-pdf/renderer": "3.4.4", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.4.0", "base64-js": "^1.5.0", "capacitor-blob-writer": "^1.1.17", "capacitor-ios-autofill-save-password": "^3.0.0", "cordova": "^10.0.0", "cross-fetch": "^3.1.4", "csv-stringify": "^6.5.1", "dommatrix": "^1.0.0", "eslint-config-react-app": "^6.0.0", "firebase": "10.8.0", "formik": "^2.2.6", "ionicons": "^5.0.0", "jay-peg": "^1.0.0", "leaflet": "^1.7.1", "lexical": "^0.11.1", "lodash": "^4.17.21", "luxon": "^1.26.0", "lz-string": "^1.5.0", "query-string": "^7.0.0", "react": "^18.3.1", "react-color": "^2.19.3", "react-csv": "^2.2.2", "react-dom": "^18.3.1", "react-error-boundary": "^4.0.11", "react-pdf": "6.2.2", "react-router": "^6.25.1", "react-router-dom": "^6.21.0", "react-signature-canvas": "^1.0.3", "react-tooltip": "^4.2.19", "rxjs": "7.4.0", "use-file-picker": "^1.4.2", "uuid": "^9.0.0", "web-streams-polyfill": "^3.0.0", "webpack": "^5.0.0", "yup": "^0.32.8", "zustand": "^4.4.7"}, "scripts": {"start": "cross-env PORT=8100 env-cmd -f .env.development react-app-rewired start", "start:staging": "env-cmd -f .env.staging react-app-rewired start", "start:production": "env-cmd -f .env.production react-app-rewired start", "build:dev": "env-cmd -f .env.development react-app-rewired build", "build:dev:hosting": "BUILD_PATH='../firebase/public' env-cmd -f .env.development react-app-rewired build", "build:staging": "env-cmd -f .env.staging react-app-rewired build", "build:staging:hosting": "BUILD_PATH='../firebase/public' env-cmd -f .env.staging react-app-rewired build", "build:production": "env-cmd -f .env.production react-app-rewired build", "build:production:hosting": "BUILD_PATH='../firebase/public' env-cmd -f .env.production react-app-rewired build", "android:open": "npx cap open android", "android:build:dev": "cross-env PLATFORM=android env-cmd -f .env.development react-app-rewired build", "android:build:staging": "cross-env PLATFORM=android env-cmd -f .env.staging react-app-rewired build", "android:build:production": "cross-env PLATFORM=android env-cmd -f .env.production react-app-rewired build", "android:profile:dev": "env-cmd -f .env.development npx cap sync android && npx cap copy android", "android:profile:staging": "env-cmd -f .env.staging npx cap sync android && npx cap copy android", "android:profile:production": "env-cmd -f .env.production npx cap sync android && npx cap copy android", "android:sync:dev": "npm run android:build:dev && npx cap sync android && npx cap copy android", "android:sync:staging": "npm run android:build:staging && npx cap sync android && npx cap copy android", "android:sync:production": "npm run android:build:production && npx cap sync android && npx cap copy android", "ios:open": "npx cap open ios", "ios:build:dev": "cross-env PLATFORM=ios env-cmd -f .env.development react-app-rewired build", "ios:build:staging": "cross-env PLATFORM=ios env-cmd -f .env.staging react-app-rewired build", "ios:build:production": "cross-env PLATFORM=ios env-cmd -f .env.production react-app-rewired build", "ios:profile:dev": "env-cmd -f .env.development npx cap sync ios && npx cap copy ios", "ios:profile:staging": "env-cmd -f .env.staging npx cap sync ios && npx cap copy ios", "ios:profile:production": "env-cmd -f .env.production npx cap sync ios && npx cap copy ios", "ios:sync:dev": "npm run ios:build:dev && npx cap sync ios && npx cap copy ios", "ios:sync:staging": "npm run ios:build:staging && npx cap sync ios && npx cap copy ios", "ios:sync:production": "npm run ios:build:production && npx cap sync ios && npx cap copy ios", "test": "react-app-rewired test --transformIgnorePatterns 'node_modules/(?!(@ionic/react|@ionic/core|@stencil/core|ionicons)/)'", "svgr": "svgr --ignore-existing --ext jsx --replace-attr-values #BFC4CA=currentColor -d src/assets/svg src/assets/svg"}, "eslintConfig": {"extends": "react-app"}, "browserslist": [">0.2%", "not dead", "not op_mini all"], "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@svgr/cli": "5.4.0", "@types/jest": "^29.5.12", "@types/leaflet": "^1.9.0", "@types/luxon": "^1.26.2", "@types/react": "^17.0.0", "@types/react-color": "^3.0.6", "@types/react-csv": "^1.1.2", "@types/react-dom": "^18.3.0", "@types/react-signature-canvas": "^1.0.1", "@types/uuid": "^9.0.0", "ajv": "^7.2.4", "assert": "^2.1.0", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "cross-env": "^7.0.3", "env-cmd": "^10.1.0", "eslint": "^7.32.0", "process": "^0.11.10", "react-app-rewired": "^2.2.1", "source-map-loader": "^5.0.0", "sourcemap-codec": "^1.4.8", "stream-browserify": "^3.0.0", "typescript": "^4.9.5", "util": "^0.12.5"}, "resolutions": {"magic-string": "^0.30.0", "react-error-overlay": "6.0.9", "@react-pdf/font": "2.2.0", "rxjs": "7.4.0"}, "description": "Cloud-based vessel compliance software"}