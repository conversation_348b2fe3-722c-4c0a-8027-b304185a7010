import { DocumentData, FieldValue, QuerySnapshot } from "firebase-admin/firestore";
import { firestore } from "../init";
import { protectFirestoreOperation } from "../lib/util_firebase";



/**
 * Makes sure all customForms that are templates have a templateCategory.
 * Probably obsolete now.
 */
export const maintainCustomFormTemplates = (results: any) => {
    results.templateCategoriesUpdated = 0;
    console.log('maintainCustomFormTemplates!');
    const templates = [] as any[];
    const licenseeIdsMap = {} as any;
    const categoriesMap = {} as any;

    const processLicenseeIds = (licenseeIds: string[]): Promise<void> => {
        if (licenseeIds.length === 0) {
            return Promise.resolve();
        }
        const licenseeId = licenseeIds.shift();
        return protectFirestoreOperation(
            `maintainCustomFormTemplates get customFormCategories ${licenseeId}`,
            () => firestore.collection('customFormCategories').where('licenseeId', '==', licenseeId).get()
        ).then((snap: QuerySnapshot<DocumentData, DocumentData>) => {
            snap.docs.forEach((doc) => {
                categoriesMap[doc.id] = doc.data();
            });
            return processLicenseeIds(licenseeIds);
        });
    };
    const processTemplates = (): Promise<void> => {
        if (templates.length === 0) {
            return Promise.resolve();
        }
        const template = templates.shift();
        if (
            template.templateCategory === undefined ||
            template.templateCategory !== categoriesMap[template.categoryId].name
        ) {
            console.log(`Updating customForms ${template.id} templateCategory ${template.templateCategory} --> ${categoriesMap[template.categoryId].name}`);
            return protectFirestoreOperation(
                `maintainCustomFormTemplates set customForms ${template.id}`,
                () => firestore.collection('customForms').doc(template.id).set({
                    templateCategory: categoriesMap[template.categoryId].name,
                    touched: FieldValue.serverTimestamp()
                }, { merge: true })
            ).then(() => {
                // Update whenLicenseeTouched
                return protectFirestoreOperation(
                    `maintainCustomFormTemplates set whenLicenseeTouched ${template.licenseeId}`,
                    () => firestore.collection('whenLicenseeTouched').doc(template.licenseeId).set({
                        customForms: FieldValue.serverTimestamp(),
                        touched: FieldValue.serverTimestamp()
                    }, { merge: true })
                );
            }).then(() => {
                results.templateCategoriesUpdated++;
                return processTemplates();
            });
        }
        return processTemplates();
    };
    return protectFirestoreOperation(
        `maintainCustomFormTemplates get customForms`,
        () => firestore.collection('customForms').where('isTemplate', '==', true).get()
    ).then((snap: QuerySnapshot<DocumentData, DocumentData>) => {
        snap.docs.forEach((doc) => {
            templates.push({
                id: doc.id,
                ...doc.data(),
                touched: FieldValue.serverTimestamp()
            });
            licenseeIdsMap[doc.data().licenseeId] = true;
        });
        return processLicenseeIds(Object.keys(licenseeIdsMap));
    })
    .then(() => {
        return processTemplates();
    });
};
