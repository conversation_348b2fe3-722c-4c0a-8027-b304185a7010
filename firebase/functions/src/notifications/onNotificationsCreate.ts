import { onDocumentCreated } from "firebase-functions/v2/firestore";
import { DocumentData, DocumentSnapshot, QuerySnapshot } from "firebase-admin/firestore";
import { settings } from "../projectSettings";
import { firestore } from "../init";
import { formatCurrency, formatRiskRating, isImage } from "../lib/util_common";
import { formatDate, formatDatetime, formatEmailReminder, formatInterval, setTimeZone } from "../lib/datesAndTime";
import { renderData, renderEmail, sendNotificationEmails } from "../lib/email";
import { defaultEventHandlerOptions } from "../eventHandlerOptions";
import { protectFirestoreOperation } from "../lib/util_firebase";


/**
 * Sends notification emails.
 */
export const onNotificationsCreate = onDocumentCreated(
    {
        ...defaultEventHandlerOptions,
        document: 'notifications/{id}',
        memory: '512MiB',
        retry: true // Will infinitely retry! Make sure all ERRORs not worth retrying are not returned.
    },
    (event) => {
        // The following code is no longer needed as V2 functions drop events after 24 hours anyway...
        // Drop old events (probably because being retried too many times)
        // const eventMaxAgeMs = 24 * 60 * 60 * 1000; // 24 hours
        // const eventAgeMs = Date.now() - Date.parse(context.timestamp);
        // if (eventAgeMs > eventMaxAgeMs) {
        //     console.error(`Dropping event ${context} with age[ms]: ${eventAgeMs}`);
        //     return Promise.resolve();
        // }

        try {
            const snapshot = event.data;
            const notification = {
                id: snapshot!.id,
                ...snapshot!.data()
            };
            return processNotification(notification).catch((e) => {
                console.error('Caught error', e);
                return Promise.resolve(); // (Don't want an infinite retry loop)
            });
        } catch (e) {
            console.error('Caught error', e);
            return Promise.resolve(); // (Don't want an infinite retry loop)
        }
    }
);


const processNotification = (notification: any) => {
    console.log('>>> processNotification! notification', notification);

    return gatherVesselNamesForNotification(notification).then(() => {
        return prepareFilesForNotification(notification);
    }).then(() => {
        if (notification.type && notification.type === 'contactEnquiry') {
            notification.to = settings.contactEnquiryTo;
            return sendNotifications(notification);
        } else if (notification.toIds) {
            // notification.toUsers = [{id, licenseeId, firstName, lastName, email}, ...]
            const userIds = [...notification.toIds];
            const toUsers = [] as any[];
            const processUserIds = (): Promise<any> => {
                if (userIds.length === 0) {
                    return Promise.resolve();
                }
                const userId = userIds.shift();
                return protectFirestoreOperation(
                    `get user ${userId}`,
                    () => firestore.collection('users').doc(userId).get()
                ).then((doc: DocumentSnapshot<DocumentData, DocumentData>) => {
                    if (
                        doc.exists &&
                        doc.data()?.state &&
                        doc.data()?.state === 'active'
                    ) {
                        const user = {
                            id: doc.id,
                            licenseeId: doc.data()?.licenseeId,
                            firstName: doc.data()?.firstName,
                            lastName: doc.data()?.lastName
                        } as any;
                        return protectFirestoreOperation(
                            `get userDetails ${userId}`,
                            () => firestore.collection('userDetails').doc(userId).get()
                        ).then((doc) => {
                            user.email = doc.data().email;
                            toUsers.push(user);
                            return processUserIds();
                        });
                    }
                    return processUserIds();
                });
            };
            return processUserIds().then(() => {
                return sendNotificationsUsingToUsers(notification, toUsers);
            });
        } else if (notification.emailMeType) {
            // notification.toUsers = [{id, licenseeId, firstName, lastName, email}, ...]
            return gatherToUsersForNotification(notification).then((toUsers) => {
                //console.log('>>> toUsers', toUsers);
                return sendNotificationsUsingToUsers(notification, toUsers);
            });
        } else {
            // Don't have enough info to send to anyone
            return Promise.reject('FAILED to send notification (no toIds or emailMeType) notification='+JSON.stringify(notification));
        }
    });

};



const gatherVesselNamesForNotification = (notification: any) => {
    if (notification.vesselIds && notification.vesselIds.length > 0) {
        notification.vesselNames = [];
        const vesselIds = [...notification.vesselIds];
        const processIds = (): Promise<any> => {
            if (vesselIds.length === 0) {
                console.log('Gathered notification.vesselNames', notification.vesselNames);
                return Promise.resolve();
            }
            const vesselId = vesselIds.shift();
            return protectFirestoreOperation(
                `get vessel ${vesselId}`,
                () => firestore.collection('vessels').doc(vesselId).get()
            ).then((snap: DocumentSnapshot<DocumentData, DocumentData>) => {
                notification.vesselNames.push(
                    snap.data()?.name
                );
            }).then(() => {
                return processIds();
            });
        };
        return processIds();
    }
    return Promise.resolve();
};
const prepareFilesForNotification = (notification: any) => {
    console.log('>>> prepareFilesForNotification', notification);
    const promises = [] as Promise<any>[];
    const filesReady = [] as any[];
    const imagesReady = [] as any[];
    if (notification.files && notification.files.length > 0) {
        notification.files.forEach((file: any) => {
            promises.push(
                getFileForNotication(notification, file).then((_file) => {
                    console.log('>>> getFileForNotication _file', _file);
                    if (_file) {
                        if (_file.isImage) {
                            imagesReady.push(_file);
                        } else {
                            filesReady.push(_file);
                        }
                    }
                })
            );
        });
    }
    return Promise.all(promises).then(() => {
        notification.files = undefined;
        if (filesReady.length > 0) {
            notification.files = filesReady;
        }
        if (imagesReady.length > 0) {
            notification.images = imagesReady;
        }
        return Promise.resolve();
    });
};
// return { name, src, isImage }
const getFileForNotication = (notification: any, fileValue: string): Promise<any> => {
    console.log('>>> getFileForNotication fileValue', fileValue);
    const fileId = fileValue.substring(1, 21);

    return protectFirestoreOperation(
        `get file ${fileId}`,
        () => firestore.collection('files').doc(fileId).get()
    ).then((doc) => {
        if (doc.exists) {
            const file = {
                id: doc.id,
                ...doc.data()
            };

            if (
                file.emailToken &&
                file.licenseeIds &&
                notification.licenseeId &&
                file.licenseeIds.indexOf(notification.licenseeId) !== -1 // Make sure file is open to the notification's licensee
            ) {
                const _isImage = isImage(file.ext);
                console.log(`>>> URL=https://firebasestorage.googleapis.com/v0/b/${settings.storageBucket}/o/files%2F${file.id}${_isImage ? '_full' : ''}.${file.ext}?alt=media&emailToken=${file.emailToken}`);
                return Promise.resolve({
                    //src: `https://firebasestorage.googleapis.com/v0/b/files/${settings.storageBucket}/o/${file.id}${_isImage ? '_full' : ''}.${file.ext}?alt=media&emailToken=${file.emailToken}`,
                    src: `https://firebasestorage.googleapis.com/v0/b/${settings.storageBucket}/o/files%2F${file.id}${_isImage ? '_full' : ''}.${file.ext}?alt=media&emailToken=${file.emailToken}`,
                    name: file.name,
                    isImage: _isImage
                });
            }
        }
        return Promise.resolve(undefined);
    });
};

// returns toUsers: an array of {id, licenseeId, email, firstName, lastName}
const gatherToUsersForNotification = (notification: any): Promise<any[]> => {
    const toUsers = [] as any[];
    console.log('gatherToUsersForNotification notification', notification);
    if (notification.vesselIds && notification.vesselIds.length > 0) {
        const userIds = {} as any;
        const users = [] as any[];
        const vesselIds = [...notification.vesselIds];
        const processIds = (): Promise<any> => {
            if (vesselIds.length === 0) {
                console.log('Gathered users', users);
                return Promise.resolve();
            }
            const vesselId = vesselIds.shift();

            return protectFirestoreOperation(
                `get vessel ${vesselId}`,
                () => firestore.collection('users').where('vesselIds', 'array-contains', vesselId).get()
            ).then((snap: QuerySnapshot<DocumentData, DocumentData>) => {
                snap.docs.forEach((doc) => {
                    const user = {
                        id: doc.id,
                        ...doc.data()
                    } as any;
                    if (
                        userIds[user.id] === undefined &&
                        user.state === 'active'
                    ) {
                        userIds[user.id] = true;
                        users.push(user);
                    }
                });
            }).then(() => {
                return processIds();
            });
        };
        const processUsers = (): Promise<any> => {
            if (users.length === 0) {
                return Promise.resolve();
            }
            const user = users.shift();
            return protectFirestoreOperation(
                `get userDetails ${user.id}`,
                () => firestore.collection('userDetails').doc(user.id).get()
            ).then((doc) => {
                const userDetails = {
                    id: doc.id,
                    ...doc.data()
                };
                if (
                    userDetails.email &&
                    user.emailMe && (
                        user.emailMe.indexOf(notification.emailMeType) !== -1 || (
                            // Having jobsUpdated emailMe means you should also get jobsCreated
                            notification.emailMeType === 'jobCreated' &&
                            user.emailMe.indexOf('jobsUpdated') !== -1
                        ) || (
                            // Having correctiveActionsUpdated emailMe means you should also get correctiveActionsCreated
                            notification.emailMeType === 'correctiveActionsCreated' &&
                            user.emailMe.indexOf('correctiveActionsUpdated') !== -1
                        ) || (
                            notification.emailMeType === 'jobCompleted' &&
                            user.emailMe.indexOf('jobsCompletedUpdated') !== -1
                        )
                    ) &&
                    user.emailMeVesselIds
                ) {
                    // For jobs, only send to users that have emailMe for that priority
                    if (['jobsCreated', 'jobsUpdated'].includes(notification.emailMeType) && notification.item.priority) {
                        // Convert '6high' to format 'jobsHigh'
                        const jobPriority = 'jobs' + notification.item.priority.charAt(1).toUpperCase() + notification.item.priority.slice(2);
                        if (!user.emailMe.includes(jobPriority)) {
                            return processUsers();
                        }
                    }
                    for (let i = 0; i < user.emailMeVesselIds.length; i++) {
                        if (notification.vesselIds.includes(user.emailMeVesselIds[i])) {
                            // Confirmed: user wants this notification
                            //notification.to.push(`${user.firstName} ${user.lastName} <${userDetails.email}>`);
                            toUsers.push({
                                id: user.id,
                                licenseeId: user.licenseeId,
                                email: userDetails.email,
                                firstName: user.firstName,
                                lastName: user.lastName
                            });
                            break;
                        }
                    }
                }
                return processUsers();
            });
        };
        return processIds().then(() => {
            return processUsers();
        }).then(() => {
            console.log('Gathered toUsers', toUsers);
            return Promise.resolve(toUsers);
        });
    }
    return Promise.resolve(toUsers);
};

//
// toUsers is an array of user objects that have email injected into them
// toUsers = [{
//      id,
//      licenseeId,
//      firstName,
//      lastName,
//      email
// }, ...]
//
const sendNotificationsUsingToUsers = (notification: any, toUsers: any[]) => {
    console.log('sendNotificationsUsingToUsers...');
    const byTimezone = {} as {
        [timezone: string]: string[]
    };
    const timezones = [] as string[];
    const processToUsers = (): Promise<any> => {
        if (toUsers.length === 0) {
            return Promise.resolve();
        }
        const toUser = toUsers.shift();
        // Get user's timezone
        return protectFirestoreOperation(
            `get userTimezone ${toUser.id}`,
            () => firestore.collection('userTimezones').doc(toUser.id).get()
        ).then((doc) => {
            if (doc.exists && doc.data().timezone) {
                return doc.data().timezone;
            }
            return protectFirestoreOperation(
                `get userTimezone ${toUser.licenseeId}`,
                () => firestore.collection('userTimezones').doc(toUser.licenseeId).get()
            ).then((doc) => {
                if (doc.exists && doc.data().timezone) {
                    return doc.data().timezone;
                } else {
                    return 'Pacific/Auckland';
                }
            });
        }).then((timezone) => {
            //console.log('found timezone='+timezone+', for toUser', toUser);
            if (byTimezone[timezone] === undefined) {
                byTimezone[timezone] = [];
                timezones.push(timezone);
            }
            byTimezone[timezone].push(`${toUser.firstName} ${toUser.lastName} <${toUser.email}>`);
            return processToUsers();
        });
    };

    const processTimezones = (): Promise<any> => {
        if (timezones.length === 0) {
            return Promise.resolve();
        }
        const timezone = timezones.shift();
        //console.log('timezone', timezone);
        notification.to = byTimezone[timezone!];
        return sendNotifications(notification, timezone).then(() => {
            return processTimezones();
        });
    };

    return processToUsers().then(() => {
        return processTimezones();
    });
}

// Converts the estimated time in milliseconds to a string in 'HH hrs  mm mins' format
const renderEstimatedTime = (estimatedTimeMilliseconds: number) => {
    if (!estimatedTimeMilliseconds || typeof (estimatedTimeMilliseconds) !== 'number') return "-"

    const timeInMinutes = Math.floor(estimatedTimeMilliseconds / 60000);
    const hoursPartial = timeInMinutes / 60;
    const hours = Math.floor(hoursPartial)
    const minutes = Math.floor((hoursPartial % 1) * 60)

    return `${hours}hrs ${minutes}mins`;
};

const sendNotifications = (notification: any, timezone?: string): Promise<any> => {
    console.log(`>>> onNotificationsCreate sendNotifications timezone=${timezone}`, notification);
    let isExpired: boolean;
    let subject: string;
    if (timezone) {
        setTimeZone(timezone);
    }
    switch (notification.type) {
        case 'contactEnquiry':
            return sendNotificationEmails(
                notification,
                'New Contact Enquiry - Sea Flux App (contact footer)',
                renderEmail(
                    'New Contact Enquiry (From within Sea Flux App)', // heading
                    '', // link
                    undefined, // status
                    `
                        ${renderData('Name', notification.form.name)}
                        ${renderData('Company', notification.form.company)}
                        ${renderData('Email', notification.form.email)}
                        ${renderData('Phone', notification.form.phone)}
                        ${renderData('Message', notification.form.message)}
                    `
                )
            );
        case 'correctiveActionCreated':
            return protectFirestoreOperation(
                `get licensee settings ${notification.licenseeSettings}`,
                () => firestore.collection('licenseeSettings').doc(notification.licenseeId).get()
            ).then((doc) => {
                return sendNotificationEmails(
                    notification,
                    notification.item.isUpdate ? 'Corrective action updated' : 'New corrective action added',
                    renderEmail(
                        notification.item.isUpdate ? 'CORRECTIVE ACTION UPDATED' : 'CORRECTIVE ACTION CREATED', // heading
                        `${settings.origin}/healthsafety?tab=CorrectiveActions&action=${notification.item.id}${notification.vesselIds.length === 1 ? `&vessels=${notification.vesselIds[0]}` : ''}`, // link
                        undefined, // isAlert, aka go red
                        `
                            ${renderData('Vessels', renderVesselLinks(notification))}
                            ${renderData('Title', notification.item.title)}
                            ${notification.item.correctiveActionNum ? renderData('Action #', notification.item.correctiveActionNum) : ''}
                            ${renderData('Description', notification.item.description)}
                            ${renderData('Assigned To', notification.item.assignedToName)}
                            ${renderData('Due Date', formatDate(notification.item.dateDue), '*')}
                        `,
                        timezone,
                        notification.images,
                        notification.files
                    )
                );
            });
        case 'correctiveActionReminder':
        case 'correctiveActionDue':
            isExpired = (notification.type === 'correctiveActionDue');
            if (isExpired) {
                subject = 'Corrective action due';
            } else { // reminder
                subject = 'Corrective action due soon';
            }

            return protectFirestoreOperation(
                `get licensee settings ${notification.licenseeSettings}`,
                () => firestore.collection('licenseeSettings').doc(notification.licenseeId).get()
            ).then((doc) => {
                return sendNotificationEmails(
                    notification,
                    subject,
                    renderEmail(
                        subject.toUpperCase(),
                        `${settings.origin}/healthsafety?tab=CorrectiveActions&action=${notification.item.id}`,
                        isExpired ? 'alert' : undefined, // isAlert, aka show red stuff
                        `
                            ${renderData('Vessels', renderVesselLinks(notification))}
                            ${renderData('Title', notification.item.title)}
                            ${notification.item.correctiveActionNum ? renderData('Action #', notification.item.correctiveActionNum) : ''}
                            ${renderData('Description', notification.item.description)}
                            ${renderData('Assigned To', notification.item.assignedToName)}
                            ${renderData('Due Date', formatDate(notification.item.dateDue), '*')}
                            ${renderData('Completed', notification.item.whenCompleted ? formatDate(notification.item.whenCompleted) : '-')}
                            ${renderData('Completed By', notification.item.completedBy)}
                        `,
                        timezone,
                        notification.images,
                        notification.files
                    )
                );
            });
        case 'hazardReported': // (deprecated) Still present to support those still using risk register v1
            return sendNotificationEmails(
                notification,
                notification.item.isUpdate ? 'Hazard report updated' : 'New hazard reported',
                renderEmail(
                    'HAZARD REPORTED', // heading
                    `${settings.origin}/vessel/${notification.vesselIds[0]}/safety?tab=HazardRegister&hazard=${notification.item.id}`, // link
                    undefined, // status
                    `
                        ${renderData('Type', 'Hazard Report')}
                        ${renderData('Vessel', renderVesselLinks(notification))}
                        ${renderData('Hazard', notification.item.name)}
                        ${renderData('Risk Notes', notification.item.riskNotes)}
                        ${renderData('Risk Rating', formatRiskRating(notification.item.riskRating))}
                        ${renderData('Key Action', notification.item.action)}
                        ${renderData('Actions Required', notification.item.actionsRequired)}
                        ${renderData('Person Responsible', notification.item.whoResponsible)}
                        ${renderData('Reported By', notification.reportedBy)}
                    `,
                    undefined, // Timezone not required
                    notification.images,
                    notification.files
                )
            );
        case 'riskAssessed':
            return sendNotificationEmails(
                notification,
                notification.item.isUpdate ? 'Risk assessment updated' : 'New risk assessed',
                renderEmail(
                    'RISK ASSESSMENT', // heading
                    `${settings.origin}/healthsafety?tab=RiskRegister&risk=${notification.item.id}`, // link
                    undefined, // status
                    `
                        ${renderData('Type', 'Risk Assessment')}
                        ${renderData('Vessel', renderVesselLinks(notification))}
                        ${renderData('Risk', notification.item.name)}
                        ${renderData('Risks Associated', notification.item.risks)}
                        ${renderData('Pre Controls Risk', `
                            <table>
                              <tr>
                                <td style="padding: 0px 20px 0px 0px"><b>Likelihood</b></td>
                                <td style="padding: 0px 0px 0px 0px">${notification.item.preControls.likelihood}</td>
                              </tr>
                              <tr>
                                <td style="padding: 0px 20px 0px 0px"><b>Consequence</b></td>
                                <td style="padding: 0px 0px 0px 0px">${notification.item.preControls.consequence}</td>
                              </tr>
                              <tr>
                                <td style="padding: 0px 20px 0px 0px"><b>Evaluation</b></td>
                                <td style="padding: 0px 0px 0px 0px">${notification.item.preControls.evaluation}</td>
                              </tr>
                            </table>
                        `, undefined, true)}
                        ${renderData('Controls', notification.item.controls)}
                        ${renderData('Post Controls Risk', `
                            <table>
                              <tr>
                                <td style="padding: 0px 20px 0px 0px"><b>Likelihood</b></td>
                                <td style="padding: 0px 0px 0px 0px">${notification.item.postControls.likelihood}</td>
                              </tr>
                              <tr>
                                <td style="padding: 0px 20px 0px 0px"><b>Consequence</b></td>
                                <td style="padding: 0px 0px 0px 0px">${notification.item.postControls.consequence}</td>
                              </tr>
                              <tr>
                                <td style="padding: 0px 20px 0px 0px"><b>Evaluation</b></td>
                                <td style="padding: 0px 0px 0px 0px">${notification.item.postControls.evaluation}</td>
                              </tr>
                            </table>
                        `, undefined, true)}
                        ${renderData('Person/s Responsible', notification.item.whoResponsible)}
                        ${renderData('Review Interval', formatInterval(notification.item.interval))}
                        ${renderData('When Last Reviewed', formatDate(notification.item.dateLastReviewed))}
                    `,
                    undefined, // Timezone not required
                    notification.images,
                    notification.files
                )
            );
        case 'incidentReported':
            return protectFirestoreOperation(
                `get incident ${notification.item.id}`,
                () => firestore.collection('incidents').doc(notification.item.id).get()
            ).then((doc) => {
                return sendNotificationEmails(
                    notification,
                    `${notification.item.type} ${notification.item.isUpdate ? 'Report Updated' : 'Reported'}`,
                    renderEmail(
                        `${notification.item.type} ${notification.item.isUpdate ? 'Report Updated' : 'Reported'}`, // heading
                        `${settings.origin}/healthsafety?tab=Incidents&report=${notification.item.id}`, // link
                        undefined, // status
                        `
                            ${renderData('Vessel', renderVesselLinks(notification))}
                            ${renderData('Report Title', notification.item.name)}
                            ${renderData('Category', notification.item.category)}
                            ${renderData('Report #', doc.exists && doc.data().reportNum)}
    
                            ${renderData('Types of personnel involved', notification.item.whoInvolvedTypes)}
                            ${renderData('First Reported By', notification.item.reportedBy)}
                            ${renderData('Role in the Event', notification.item.role)}
                            ${renderData('Person(s) involved', notification.item.whoInvolved)}
                            ${renderData('Witnesses', notification.item.witnesses)}
    
                            ${renderData('Date & Time', formatDatetime(notification.item.whenEvent, ', '), '*')}
                            ${renderData('Event Location', notification.item.location)}
                            ${renderData('Environmental Conditions', notification.item.conditions)}
                            ${renderData('Notified Authorities?', notification.item.notifiedAuthorities)}
                            ${renderData('Believed causes of this event', notification.item.causes)}
                            ${renderData('Damage To Property', notification.item.propertyDamage)}
                            ${renderData('Description', notification.item.description)}
                            ${renderData('Initial Actions Taken', notification.item.initialActions)}
                            ${renderData('Corrective Actions Suggested', notification.item.prevention)}
    
                            ${notification.item.injuries.map((injury: any, index: number) => {
                                    return (
                                        renderData(`Person Injured${notification.item.injuries.length > 1 ? ` (${index + 1})` : ''}`, injury.whoInjured) +
                                        renderData('Type', injury.type) +
                                        renderData('Location', injury.location) +
                                        renderData('Outcome', injury.outcome)
                                    );
                                }).join('')
                            }
                        `,
                        timezone,
                        notification.images,
                        notification.files
                    )
                );
            });
        case 'jobCreated':
            return protectFirestoreOperation(
                `get licensee settings ${notification.licenseeSettings}`,
                () => firestore.collection('licenseeSettings').doc(notification.licenseeId).get()
            ).then((doc) => {
                const hasTimeTrackingEnabled = (doc.exists && doc.data().hasMaintenanceTaskTime) ?? false;
                return sendNotificationEmails(
                    notification,
                    notification.item.isUpdate ? 'Job updated' : 'New job added',
                    renderEmail(
                        notification.item.isUpdate ? 'JOB UPDATED' : 'JOB CREATED', // heading
                        `${settings.origin}/vessel/${notification.vesselIds[0]}/maintenance?tab=JobList&job=${notification.item.id}`, // link
                        undefined,
                        `
                            ${renderData('Vessel', renderVesselLinks(notification))}
                            ${renderData('Task', notification.item.task)}
                            ${notification.item.jobNum ? renderData('Job #', notification.item.jobNum) : ''}
                            ${renderData('Description', notification.item.description)}
                            ${renderData('Priority', notification.item.priority.charAt(1).toUpperCase() + notification.item.priority.slice(2))}
                            ${renderData('Assigned To', notification.item.assignedToName)}
                            ${renderData('System', notification.item.system)}
                            ${renderData('Equipment', notification.item.equipment)}
                            ${renderData('Location', notification.item.location)}
                            ${renderData('Critical Equipment', notification.item.isCritical ? 'Yes' : 'No')}
                            ${renderData('Due Date', formatDate(notification.item.dateDue))}
                            ${renderData('Estimated Cost', notification.item.estimatedCost)}
                            ${hasTimeTrackingEnabled ? renderData('Estimated Time', !!notification.item.estimatedTime ? renderEstimatedTime(notification.item.estimatedTime) : '-') : ''}
                        `,
                        undefined, // Timezone not required
                        notification.images,
                        notification.files
                    )
                );});
        case 'jobCompleted':
            return protectFirestoreOperation(
                `get licensee settings ${notification.licenseeSettings}`,
                () => firestore.collection('licenseeSettings').doc(notification.licenseeId).get()
            ).then((doc) => {
                const hasTimeTrackingEnabled = (doc.exists && doc.data().hasMaintenanceTaskTime) ?? false;
                return sendNotificationEmails(
                    notification,
                    notification.item.isUpdate ? 'Completed job updated' : 'Completed job',
                    renderEmail(
                        notification.item.isUpdate ? 'COMPLETED JOB UPDATED' : 'COMPLETED JOB', // heading
                        `${settings.origin}/vessel/${notification.vesselIds[0]}/maintenance?tab=MaintenanceHistory&task=${notification.item.id}`, // link
                        'completed',
                        `
                            ${renderData('Vessel', renderVesselLinks(notification))}
                            ${renderData('Job', notification.item.task)}
                            ${notification.item.jobNum ? renderData('Job #', notification.item.jobNum) : ''}
                            ${renderData('Description', notification.item.description)}
                            ${renderData('System', notification.item.system)}
                            ${renderData('Equipment', notification.item.equipment)}
                            ${renderData('Location', notification.item.location)}
                            ${renderData('Critical Equipment', notification.item.isCritical ? 'Yes' : 'No')}
                            ${renderData('When Completed', formatDate(notification.item.whenCompleted))}
                            ${renderData('Completed By', notification.item.completedByName)}
                            ${hasTimeTrackingEnabled ? renderData('Actual Time', notification.item.actualTime ? renderEstimatedTime(notification.item.actualTime) : '-') : ''}
                            ${renderData('Notes', notification.item.notes)}
                            ${renderData('Job Tags', notification.item.jobTags ? notification.item.jobTags.join(', ') : '-')}
                            ${renderData('Maintenance Tags', notification.item.maintenanceTags ? notification.item.maintenanceTags.join(', ') : '-')}
                            ${renderData('Spare Parts Used', notification.item.partsUsed)}
                        `,
                        undefined, // Timezone not required
                        notification.images,
                        notification.files
                    )
                );
            });
        case 'safetyCheckFaultReported':
            return sendNotificationEmails(
                notification,
                notification.item.isUpdate ? 'Safety check item fault updated' : 'New safety check item fault reported',
                renderEmail(
                    notification.item.isUpdate ? 'FAULT UPDATED' : 'FAULT REPORTED', // heading
                    `${settings.origin}/vessel/${notification.vesselIds[0]}/safety?tab=SafetyEquipmentChecks&safetyItem=${notification.item.id}`, // link
                    'alert', // status
                    `
                        ${renderData('Vessel', renderVesselLinks(notification))}
                        ${renderData('Date Reported', formatDate(notification.item.whenCompleted))}
                        ${renderData('Reported By', notification.reportedBy)}
                        ${renderData('Faulty Safety Item', notification.item.safetyItem.item)}
                        ${renderData('Location', notification.item.safetyItem.location)}
                        ${renderData('Critical Equipment', notification.item.safetyItem.isCritical ? 'Yes' : 'No')}
                        ${renderData('Safety Check Task', notification.item.safetyItem.description)}
                        ${renderData('Notes', notification.item.notes)}
                    `,
                    undefined, // Timezone not required
                    notification.images,
                    notification.files
                )
            );
        case 'safetyEquipmentReminder':
        case 'safetyEquipmentExpired':
            isExpired = (notification.type === 'safetyEquipmentExpired');
            if (isExpired) {
                subject = (notification.item.type === 'expiring') ? 'Safety equipment expired' : 'Safety equipment service due';
            } else { // reminder
                subject = (notification.item.type === 'expiring') ? 'Safety equipment expiring soon' : 'Safety equipment service due soon';
            }
            return sendNotificationEmails(
                notification,
                subject,
                renderEmail(
                    subject.toUpperCase(),
                    `${settings.origin}/vessel/${notification.vesselIds[0]}/safety?tab=SafetyEquipmentList&item=${notification.item.id}`,
                    isExpired ? 'alert' : undefined, // status
                    `
                        ${renderData('Type', 'Safety Equipment')}
                        ${renderData('Vessel', renderVesselLinks(notification))}
                        ${renderData('Safety Item', notification.item.item)}
                        ${renderData((notification.item.type === 'expiring') ? 'Expiry Date' : 'Next Service', formatDate(notification.item.dateDue))}
                        ${renderData('Description', notification.item.description)}
                        ${renderData('Location', notification.item.location)}
                        ${renderData('Critical Equipment', notification.item.isCritical ? 'Yes' : 'No')}
                        ${renderData('Quantity', notification.item.quantity)}
                    `,
                    undefined, // Timezone not required
                    notification.images,
                    notification.files
                )
            );
        case 'vesselCertificateReminder':
        case 'vesselCertificateExpired':
            isExpired = (notification.type === 'vesselCertificateExpired');
            if (isExpired) {
                subject = `${notification.item.isShoreFacility ? 'Vehicle certification' : 'Vessel certificate'} expired`;
            } else { // reminder
                subject = `${notification.item.isShoreFacility ? 'Vehicle certification' : 'Vessel certificate'} expiring soon`;
            }
            return sendNotificationEmails(
                notification,
                subject,
                renderEmail(
                    subject.toUpperCase(),
                    `${settings.origin}/vessel/${notification.vesselIds[0]}/documents?tab=Certifications&item=${notification.item.id}`,
                    isExpired ? 'alert' : undefined, // status
                    `
                        ${renderData('Type', notification.item.isShoreFacility ? 'Vehicle certification' : 'Vessel certificate')}
                        ${renderData(notification.item.isShoreFacility ? 'Shore Based Facility' : 'Vessel', renderVesselLinks(notification))}
                        ${renderData(notification.item.isShoreFacility ? 'Certification' : 'Certificate', notification.item.title)}
                        ${renderData(notification.item.isShoreFacility ? 'Certification number' : 'Certificate Number', notification.item.certNum)}
                        ${renderData('Expiry Date', formatDate(notification.item.dateExpires))}
                    `,
                    undefined, // Timezone not required
                    notification.images,
                    notification.files
                )
            );
        case 'vesselDocumentReminder':
        case 'vesselDocumentExpired':
            isExpired = (notification.type === 'vesselDocumentExpired');
            if (isExpired) {
                subject = 'Vessel document expired';
            } else { // reminder
                subject = 'Vessel document expiring soon';
            }
            return sendNotificationEmails(
                notification,
                subject,
                renderEmail(
                    subject.toUpperCase(),
                    `${settings.origin}/vessel/${notification.vesselIds[0]}/documents?tab=Documents&item=${notification.item.id}`,
                    isExpired ? 'alert' : undefined, // status
                    `
                        ${renderData('Type', 'Vessel Document')}
                        ${renderData('Vessel', renderVesselLinks(notification))}
                        ${renderData('Document', notification.item.title)}
                        ${renderData('Expiry Date', formatDate(notification.item.dateExpires))}
                    `,
                    undefined, // Timezone not required
                    notification.images,
                    notification.files
                )
            );
        case 'companyPlanReminder':
        case 'companyPlanExpired':
            isExpired = (notification.type === 'companyPlanExpired');
            if (isExpired) {
                subject = `${notification.item.title} due`;
            } else { // reminder
                subject = `${notification.item.title} due soon`;
            }
            return sendNotificationEmails(
                notification,
                subject,
                renderEmail(
                    subject.toUpperCase(),
                    `${settings.origin}/documents?tab=Plan`,
                    isExpired ? 'alert' : undefined, // status
                    `
                        ${renderData('Document', notification.item.title)}
                        ${renderData('Due Date', formatDate(notification.item.dateExpires))}
                    `,
                    timezone,
                    notification.images,
                )
            );
        case 'companyDocumentReminder':
        case 'companyDocumentExpired':
            isExpired = (notification.type === 'companyDocumentExpired');
            if (isExpired) {
                subject = 'Company document expired';
            } else { // reminder
                subject = 'Company document expiring soon';
            }
            return sendNotificationEmails(
                notification,
                subject,
                renderEmail(
                    subject.toUpperCase(),
                    `${settings.origin}/documents?tab=CompanyDocuments&item=${notification.item.id}`,
                    isExpired ? 'alert' : undefined, // status
                    `
                        ${renderData('Document', notification.item.title)}
                        ${renderData('Expiry Date', formatDate(notification.item.dateExpires))}
                    `,
                    undefined, // Timezone not required
                    notification.images,
                    notification.files
                )
            );
        case 'crewCertificateReminder':
        case 'crewCertificateExpired':
            isExpired = (notification.type === 'crewCertificateExpired');
            if (isExpired) {
                subject = 'Crew certificate expired';
            } else { // reminder
                subject = 'Crew certificate expiring soon';
            }
            return sendNotificationEmails(
                notification,
                subject,
                renderEmail(
                    subject.toUpperCase(),
                    `${settings.origin}/crew?tab=CrewCertificates&item=${notification.item.id}`,
                    isExpired ? 'alert' : undefined, // status
                    `
                        ${renderData('Type', 'Crew Certificate')}
                        ${renderData('Certificate', notification.item.title)}
                        ${renderData('Crew Member', notification.item.heldBy)}
                        ${renderData('Expiry Date', formatDate(notification.item.dateExpires))}
                        ${renderData('Issued By', notification.item.issuedBy)}
                    `,
                    undefined, // Timezone not required
                    notification.images,
                    notification.files
                )
            );
        case 'safetyMeetingReported':
            return sendNotificationEmails(
                notification,
                notification.item.isUpdate ? 'Safety meeting report updated' : 'Safety meeting report',
                renderEmail(
                    notification.item.isUpdate ? 'SAFETY MEETING REPORT UPDATED' : 'SAFETY MEETING REPORT', // heading
                    `${settings.origin}/healthsafety?tab=HealthSafetyMeetings&vessels=${notification.vesselIds[0]}`, // link
                    undefined, // status
                    `
                        ${renderData('Vessels / Facilities', renderVesselLinks(notification))}
                        ${renderData('Date & Time', formatDate(notification.item.whenMeeting))}
                        ${renderData('Meeting Notes', notification.item.notes)}
                        ${renderData('Personnel Present', renderNames(notification.item.personnelPresentNames))}
                    `,
                    undefined, // Timezone not required
                    notification.images,
                    notification.files
                )
            );
        case 'safetyMeetingReminder':
            let todayOrTomorrow;
            switch (notification.item.emailReminder) {
                case '0d': todayOrTomorrow = 'today';
                case '1d': todayOrTomorrow = 'tomorrow';
            };
            subject = `Health & Safety Meeting due ${todayOrTomorrow ? todayOrTomorrow : 'soon'}`;
            return sendNotificationEmails(
                notification,
                subject,
                renderEmail(
                    subject.toUpperCase(),
                    `${settings.origin}/healthsafety?tab=HealthSafetyMeetings&vessels=${notification.vesselIds[0]}`,
                    undefined,
                    `
                        ${renderData('Vessel', renderVesselLinks(notification))}
                        ${renderData('Date of Upcoming Meeting', formatDate(notification.item.dateDue))}
                        ${renderData('Meeting Interval', formatInterval(notification.item.interval))}
                        ${renderData('Email Reminder', formatEmailReminder(notification.item.emailReminder))}
                    `,
                    undefined, // Timezone not required
                    undefined,
                    undefined
                )
            );
        case 'jobReminder':
        case 'jobDue':
            isExpired = (notification.type === 'jobDue');
            if (isExpired) {
                subject = 'Job completion due';
            } else { // reminder
                subject = 'Job completion due soon';
            }

            return protectFirestoreOperation(
                `get licensee settings ${notification.licenseeSettings}`,
                () => firestore.collection('licenseeSettings').doc(notification.licenseeId).get()
            ).then((doc) => {
                const hasTimeTrackingEnabled = (doc.exists && doc.data().hasMaintenanceTaskTime) ?? false;
            return sendNotificationEmails(
                notification,
                subject,
                renderEmail(
                    subject.toUpperCase(),
                    `${settings.origin}/vessel/${notification.vesselIds[0]}/maintenance?tab=JobList&item=${notification.item.id}`,
                    isExpired ? 'alert' : undefined, // status
                    `
                        ${renderData('Vessel', renderVesselLinks(notification))}
                        ${renderData('Task', notification.item.task)}
                        ${notification.item.jobNum ? renderData('Job #', ''+notification.item.jobNum) : ''}
                        ${renderData('Description', notification.item.description)}
                        ${renderData('Priority', notification.item.priority.charAt(1).toUpperCase() + notification.item.priority.slice(2))}
                        ${renderData('Assigned To', notification.item.assignedToName)}
                        ${renderData('System', notification.item.system)}
                        ${renderData('Equipment', notification.item.equipment)}
                        ${renderData('Location', notification.item.location)}
                        ${renderData('Critical Equipment', notification.item.equipment ? (notification.item.isCritical ? 'Yes' : 'No') : '-')}
                        ${renderData('Due Date', formatDate(notification.item.dateDue))}
                        ${renderData('Estimated Cost', notification.item.estimatedCost)}
                        ${hasTimeTrackingEnabled ? renderData('Estimated Time', !!notification.item.estimatedTime ? renderEstimatedTime(notification.item.estimatedTime) : '-') : ''}
                    `,
                    undefined, // Timezone not required
                    notification.images,
                    notification.files
                )
            );});
    }
    return Promise.reject(`notification.type not found for ${notification?.type}`);
};

const renderVesselLinks = (notification: any) => {
    let s = '';
    for (let i = 0; i < notification.vesselIds.length; i++) {
        if (i > 0) {
            s += ', ';
        }
        s += `<a href="${settings.origin}/vessel/${notification.vesselIds[i]}">${notification.vesselNames[i]}</a>`;
    }
    return s;
};

const renderNames = (names?: string[]) => {
    let s = '';
    if (names && names.length > 0) {
        for (let i = 0; i < names.length; i++) {
            if (i > 0) {
                s += ', ';
            }
            s += names[i];
        }
    }
    return s;
};
