type Settings = {
    mailgunDomain: string;
    /** For use with Google Cloud Secret Manager */
    mailgunApiKeySecretResourceName: string;
    /** Most emails sent by the server are FROM this email address */
    fromSeaFluxEmail: string;
    env: 'development' | 'staging' | 'production';
    name: string;
    /** Where the Sea Flux website is being hosted */
    origin: string;
    firebaseApiKey: string;
    storageBucket: string;
    storageArchiveBuckets: string[];
    /** Stops emails being sent to their intended recipients. Important when not in production so we don't spam real customers. */
    preventDirectEmails: boolean;
    /** List of email addresses to send a copy of weekly report emails to */
    dailyNotificationCopyTo: string[] | undefined;
    /** List of email addresses to send a copy of weekly report emails to */
    weeklyReportCopyTo: string[] | undefined;
    /** List of email addresses to send a copy of welcome emails to */
    welcomeEmailCopyTo: string[] | undefined;
    /** Catchall list of email addresses to send a copy of emails to. Only used if one of the preceeding *To fields is not relevant. */
    emailCopyTo: string[] | undefined;
    /** Email addresses to send errorReports to */
    errorReportsTo: string[];
    /** Email addresses to send contact enquiries to */
    contactEnquiryTo: string[];
};

type ProjectSettings = {
    [projectId: string]: Settings
}

const commonSettings = {
    mailgunDomain: 'mailgun.seaflux.co.nz',
    fromSeaFluxEmail: 'Sea Flux <<EMAIL>>'
};

const projectSettings: ProjectSettings = {
    'sea-flux-3c853': {
        ...commonSettings,
        env: 'production',
        name: 'Sea Flux',
        origin: 'https://seaflux.netlify.app',
        firebaseApiKey: 'AIzaSyDLRHc5XwKt1IcDFdWO1-iR91nresovlWk',
        storageBucket: 'sea-flux-3c853.appspot.com',
        storageArchiveBuckets: ['sea-flux-archive-a','sea-flux-archive-b'],
        preventDirectEmails: false,
        dailyNotificationCopyTo: undefined,
        weeklyReportCopyTo: undefined,
        welcomeEmailCopyTo: ['<EMAIL>'],
        emailCopyTo: undefined,
        errorReportsTo: ['Ben Robinson <<EMAIL>>', 'Tai Ellis <<EMAIL>>'],
        contactEnquiryTo: ['Tai Ellis <<EMAIL>>'],
        mailgunApiKeySecretResourceName: 'projects/653465357807/secrets/mailgun-api-key/versions/latest'
    },
    'sea-flux-development': {
        ...commonSettings,
        env: 'development',
        name: 'Sea Flux Development',
        origin: 'http://localhost:8081',
        firebaseApiKey: 'AIzaSyDjh1sUdvK7FOI0Oxmtid7fTrLBHtTrAeQ',
        storageBucket: 'sea-flux-development.appspot.com',
        storageArchiveBuckets: ['sea-flux-development-archive-a'],
        preventDirectEmails: true, // Don't want production clients to receive emails
        dailyNotificationCopyTo: undefined,
        weeklyReportCopyTo: undefined,
        welcomeEmailCopyTo: ['Dev Platform Emails <<EMAIL>>', 'Ben Robinson <<EMAIL>>'],
        emailCopyTo: ['Dev Platform Emails <<EMAIL>>', 'Ben Robinson <<EMAIL>>'],
        errorReportsTo: ['Dev Platform Emails <<EMAIL>>', 'Ben Robinson <<EMAIL>>'],
        contactEnquiryTo: ['Dev Platform Emails <<EMAIL>>', 'Ben Robinson <<EMAIL>>'],
        mailgunApiKeySecretResourceName: 'projects/1080199825213/secrets/mailgun-api-key/versions/latest'
    },
    'sea-flux-staging': {
        ...commonSettings,
        env: 'staging',
        name: 'Sea Flux Staging',
        origin: 'https://seaflux-staging.netlify.app',
        firebaseApiKey: 'AIzaSyCEJTooMkaGaISMCd1z14fKV363DJuBmX8',
        storageBucket: 'sea-flux-staging.appspot.com',
        storageArchiveBuckets: ['sea-flux-staging-archive-a'],
        preventDirectEmails: true, // Don't want production clients to receive emails
        dailyNotificationCopyTo: undefined,
        weeklyReportCopyTo: undefined,
        welcomeEmailCopyTo: ['Sea Flux NZ <<EMAIL>>', 'Staging Platform Emails <<EMAIL>>', 'Vaughan Ellis <<EMAIL>>', 'Ben Robinson <<EMAIL>>'],
        emailCopyTo: ['Sea Flux NZ <<EMAIL>>', 'Staging Platform Emails <<EMAIL>>', 'Vaughan Ellis <<EMAIL>>', 'Ben Robinson <<EMAIL>>'],
        errorReportsTo: ['Sea Flux NZ <<EMAIL>>', 'Staging Platform Emails <<EMAIL>>', 'Vaughan Ellis <<EMAIL>>', 'Ben Robinson <<EMAIL>>'],
        contactEnquiryTo: ['Sea Flux NZ <<EMAIL>>', 'Staging Platform Emails <<EMAIL>>', 'Vaughan Ellis <<EMAIL>>', 'Ben Robinson <<EMAIL>>'],
        mailgunApiKeySecretResourceName: 'projects/668969870326/secrets/mailgun-api-key/versions/latest'
    }
};

export const projectId = process.env.GCLOUD_PROJECT as string;
export const settings = projectSettings[projectId];
