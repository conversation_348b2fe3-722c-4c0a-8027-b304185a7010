import { DocumentData, FieldValue, QuerySnapshot } from "firebase-admin/firestore";
import { firestore } from "../../init";
import { processTasks, protectFirestoreOperation } from "../../lib/util_firebase";



/**
 * 
 */
export const fixBadCustomFormsFileRefs = (results: any) => {
    console.log(`fixBadCustomFormsFileRefs!`);
    const files = [] as any[];
    let count = 0;
    results.filesWithBadCustomFormsRefsFixed = 0;
    return protectFirestoreOperation(
        `Get customForm files`,
        () => firestore.collection('files').where('uploadFor.collection', '==', 'customForms').get()
    ).then((snap: QuerySnapshot<DocumentData, DocumentData>) => {
        snap.docs.forEach((doc) => {
            const file = {
                id: doc.id,
                ...doc.data()
            } as any;
            if (file.refs) {
                file.refs.forEach((ref: any) => {
                    if (ref.collection === 'customForms') {
                        files.push(file);
                    }
                });
            }
        });
        return processTasks(
            files,
            (file) => {
                count++;
                let n = 0;
                let problemRef: any;
                file.refs.forEach((ref: any) => {
                    if (ref.collection === 'customForms') {
                        problemRef = ref;
                        n++;
                    }
                });
                const versions = [] as any[];
                return firestore.collection('customFormVersions').where('customFormId', '==', problemRef.docId).get().then((snap) => {
                    snap.docs.forEach((doc) => {
                        versions.push({
                            id: doc.id,
                            ...doc.data()
                        });
                    });
                    console.log(`(${count}) Found a bad ref file.uploadFor.collection=${file.uploadFor ? file.uploadFor.collection : '(no uploadFor)'} versionsFound=${versions.length}`, file);
                    return versions;
                }).then((versions) => {
                    const newRefs = file.refs;
                    const oldRefs = [...newRefs];
                    // Remove customForms reference
                    for (let i = newRefs.length - 1; i >= 0; i--) {
                        if (newRefs[i].collection === 'customForms') {
                            newRefs.splice(i, 1);
                        }
                    }
                    return processTasks(
                        versions,
                        (version) => {
                            let matchFound = false;
                            Object.values(version.form).forEach((element: any) => {
                                if (element && element.help && element.help.files) {
                                    element.help.files.forEach((fileValue: string) => {
                                        const fileId = fileValue.substring(1,21);
                                        if (file.id === fileId) {
                                            //console.log(`File found in element: ${fileValue}`);
                                            matchFound = true;
                                            // Also check it's not already in newRefs
                                            newRefs.forEach((ref: any) => {
                                                if (ref.docId === version.id) {
                                                    matchFound = false;
                                                }
                                            });
                                        }
                                    });
                                }
                            });
                            if (matchFound) {
                                newRefs.push({
                                    docId: version.id,
                                    collection: 'customFormVersions'
                                });
                            }
                            return Promise.resolve();
                        }
                    ).then(() => {
                        // Update refs
                        return firestore.collection('files').doc(file.id).set(
                            {
                                refs: newRefs,
                                triggerCheck: FieldValue.serverTimestamp() // will cause any new state to be propagated
                            },
                            { merge: true }
                        );
                    }).then(() => {
                        results.filesWithBadCustomFormsRefsFixed++;
                    });
                });
            }
        );
    }).then(() => {
        console.log(`Found ${count} instances of ref.collection=customForms! fixed ${results.filesWithBadCustomFormsRefsFixed}`);
    });
};
