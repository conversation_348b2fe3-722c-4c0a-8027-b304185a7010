import { firestore } from "../../init";
import { processTasks, protectFirestoreOperation } from "../../lib/util_firebase";
import { sendMail } from "../../lib/email";
import { settings } from "../../projectSettings";
import { DocumentData, FieldValue, QuerySnapshot } from "firebase-admin/firestore";
import { formatTimeDuration, makeDateTime, setTimeZone } from "../../lib/datesAndTime";

/**
 * Hmmmmm... wouldn't it be nice if by convention
 *  - when* is a time
 *  - date* is just a date
 */

const dateFields = {
    // customForms: { // Contain SeaDate data within custom elements. Need custom code to process customFormsCompleted
    //         whenLastCompleted is a time
    // }
    companyDocuments: {
        whenExpires: true,
        whenToRemind: true
    },
    companyPlans: {
        lastReviewDate: 'dateLastReviewed',
        // Should rename this to dateLastReviewed (TODO!)
        whenDue: true,
        whenToRemind: true
    },
    correctiveActions: {
        whenDue: true,
        whenToRemind: true
    },
    crewCertificates: {
        whenExpires: true,
        whenIssued: true,
        whenToRemind: true
    },
    dangerousGoods: {
        whenExpires: true
    },
    drillReports: {
        drills: {
            // array
            whenDue: true
        },
        whenCompleted: true
    },
    drills: {
        crew: {
            '*': {
                whenDue: true,
                whenLastCompleted: true
            }
        },
        whenDue: true,
        whenLastCompleted: true
    },
    incidentReviews: {
        // whenCompleted is a time
    },
    incidents: {
        // whenEvent is a time
        // whenCompleted is a time
    },
    jobs: {
        whenDue: true,
        whenToRemind: true,
        // whenCompleted is a time
    },
    maintenanceTasksCompleted: {
        // whenCompleted is a time
    },
    mfaEmailPasscodes: {
        // whenExpires is a time
    },
    passwordResets: {
        // whenExpires is a time
        // whenReset is a time
    },
    risks: {
        whenDue: true,
        whenLastReviewed: true
    },
    risksReviewed: {
        whenReviewed: true
    },
    safetyCheckCompleted: {
        // whenCompleted is a time
    },
    safetyCheckItems: {
        whenDue: true
        // whenLastChecked is a time based on safetyCheckCompleted.whenCompleted
    },
    safetyEquipmentItems: {
        whenDue: true,
        whenToRemind: true,
        // whenLastChecked is a time based on safetyEquipmentTaskCompleted.whenCompleted
    },
    safetyEquipmentTaskCompleted: {
        // whenCompleted is a time
        whenExpires: true
    },
    safetyMeetingReports: {
        // whenMeeting is a time
    },
    scheduledMaintenanceTasks: {
        // whenLastService is a time
        whenDue: true // Calculated using whenLastService
    },
    SOPs: {
        whenIssued: true
    },
    SOPsCompleted: { // Obsolete collection
        whenCompleted: true
    },
    surveyReports: {
        whenSurveyed: true
    },
    trainingTaskReports: {
        whenCompleted: true,
        whenDue: true
    },
    trainingTasks: {
        whenDue: true,
        whenLastCompleted: true
    },
    userDetails: {
        dateOfBirth: true
    },
    users: {
        whenInducted: true
        // whenRoleChange is a time
        // whenWelcomed is a time
    },
    vesselCertificates: {
        whenExpires: true,
        whenIssued: true,
        whenToRemind: true
    },
    vesselDocuments: {
        whenExpires: true,
        whenToRemind: true
    },
    vessels: {
        safetyMeetingSettings: {
            whenToRemind: true,
            whenDue: true,
            // whenMeeting is a time
        },
        whenLaunched: true
    },
    voyages: { // Just times
        // whenDeparted: true,
        // whenArrived: true,
        // whenTripReported: true,
        // trips: { // array
        //         whenDeparted: true,
        //         whenArrived: true,
        //         stops: { // array
        //                 whenArrived: true,
        //                 whenDeparted: true
        //         }
        // }
        // "Start up" for Multi trip has a SeaDate!
        //     newData.whenDeparted = combineDateAndHours24(baseDate, trip.whenDeparted);
        //     Looks like it ends up as a specific time, so we can ignore
    }
};


/**
 * Make sure all dates are simple ISO dates like YYYY-MM-DD.
 * Most dates currently (as at 17-02-2024) are recorded in millis and meant to represent 00:00 within Pacific/Auckland timezone.
 * The problem is there's a significant number that record the time as 00:00 relative to the user-device's timezone.
 * We want to, at least for now, conform all dates to a simple ISO date string YYYY-MM-DD.
 * Also, we'll change all the names of fields that represent dates (when*) to be (date*) so there is no confusion with times in millis.
 * We'll leave the original when* values around as when*_old values so we're not losing any data in the process and could potentially backtrack.
 * In an effort to convert all millis values to simple dates, we can just convert the millis to a date within the NZ timezone.
 * This strategy should work because
 * A. Most dates were recorded in this timezone
 * B. All other timezone's 00:00 values fall within the same NZ day.
 *    i.e. The nz 24-hour-day should cover all the begginings of all the other timezone's days.
 * 
 * There are other problems we'll solve here
 * - Some dates are negative millis values working out to years like 0025. This can happen if the user only entered 2 digits for the year.
 * - Some dates are NaN - we'll delete them
 * - Some dates are empty strings - we'll delete them
 * 
 */
export const conformAllDates = (results: any) => {
    console.log(`conformAllDates!`);
    //return gatherWhenFields(results);
    const collections = Object.keys(dateFields);
    setTimeZone("Pacific/Auckland");
    results.datesConformed = {};

    const timeCounts = {} as any;
    let nanCount = 0;
    let negatives = 0;
    let zeroes = 0;
    let before1900 = 0;
    let after2200 = 0;

    const processCollection = (collection: string) => {
        if (Object.keys((dateFields as any)[collection]).length === 0) { // No date fields for this collection
            return Promise.resolve();
        }
        return protectFirestoreOperation(
            `Get all from ${collection}`,
            () => firestore.collection(collection).get()
        ).then((snap: QuerySnapshot<DocumentData, DocumentData>) => {
            const documents = snap.docs.map((doc) => {
                return {
                    id: doc.id,
                    ...doc.data()
                } as any;
            });
            console.log(`[] ${collection} (${documents.length})`);
            return processTasks(
                documents,
                (document: any) => {
                    return processDocument(collection, document);
                }, {
                    batchSize: 25,
                    logProgressEvery: 1000,
                    itemsName: collection
                }
            )
        });
    };

    const processDocument = (collection: string, document: any) => {
        const updateData = {} as any;
        let updates = 0;

        const processFields = (fieldData: any, data: any, updateData: any, withinArray = false) => {
            // Process keys
            Object.keys(fieldData).forEach((field) => {
                if (field === '*') {
                    // Match all fields
                    Object.keys(data).forEach((key) => {
                        updateData[key] = {};
                        processFields(fieldData['*'], data[key], updateData[key], withinArray);
                    });
                } else if (data[field] !== undefined) {
                    if (typeof data[field] === 'string') {
                        if (field === fieldData[field]) {
                            // Already converted
                            return;
                        }
                        // Delete strings - no when* field should be a string
                        if (withinArray) {
                            delete updateData[field];
                        } else {
                            updateData[field] = FieldValue.delete();
                        }
                        updates++;
                    } else if (typeof data[field] === 'number') {
                        if (isNaN(data[field])) {
                            // Delete NaNs
                            nanCount++;
                            if (withinArray) {
                                delete updateData[field];
                            } else {
                                updateData[field] = FieldValue.delete();
                            }
                            updates++;
                            return;
                        }

                        let dateTime = makeDateTime(data[field]);
                        //dateTime = dateTime.setZone('Pacific/Auckland');

                        const dateString = dateTime.toISOTime() as string;
                        let fixed = dateTime.toISO() as string;
                        if (data[field] === 0) {
                            zeroes++;
                        } else if (data[field] < 0) {
                            negatives++;
                            // This looks like it's due to user's entering a year without the first 2 digits
                            // e.g. instead of 2024, it's 0024
                            // We can fix by adding 2000 to the year.
                            dateTime = dateTime.set({ year: dateTime.get('year')+2000 });
                            fixed = dateTime.toISO() as string;
                        } else if (data[field] < makeDateTime('1900-01-01').toMillis()) {
                            before1900++;
                        } else if (data[field] > makeDateTime('2200-01-01').toMillis()) {
                            after2200++;
                        }

                        if (timeCounts[dateString] === undefined) {
                            timeCounts[dateString] = {
                                time: dateString,
                                count: 0,
                                example: `${data[field]} ${makeDateTime(data[field]).toISODate()} --> ${fixed}`,
                                date: makeDateTime(data[field]).toISODate()
                            };
                        }
                        timeCounts[dateString].count++;

                        if (field.startsWith('when')) {
                            updateData[`${field}_old`] = data[field]; // store old value
                            updateData[`date${field.substring(4)}`] = dateTime.toISODate(); // add new value
                            // delete original field
                            if (withinArray) {
                                delete updateData[field];
                            } else {
                                updateData[field] = FieldValue.delete();
                            }
                        } else if (typeof fieldData[field] === 'string') {
                            // new field name specified in dateFields config
                            updateData[`${field}_old`] = data[field]; // store old value
                            updateData[fieldData[field]] = dateTime.toISODate();
                            // delete original field
                            if (withinArray) {
                                delete updateData[field];
                            } else {
                                updateData[field] = FieldValue.delete();
                            }
                        } else if (field === 'dateOfBirth') {
                            updateData[`${field}_old`] = data[field]; // store old value
                            updateData[`date${field.substring(4)}`] = dateTime.toISODate(); // add new value
                        } else {
                            console.error(`>>>>>>>>>>>>>>>>> THIS SHOULD NEVER HAPPEN!`);
                        }
                        updates++;
                    } else if (Array.isArray(data[field])) {
                        // Deal with array
                        updateData[field] = data[field]; // <-- Careful, this is a a direct copy of the original data
                        for (let i = 0; i < data[field].length; i++) {
                            processFields(fieldData[field], data[field][i], updateData[field][i], true);
                        }
                    } else {
                        // Deal with more keys
                        updateData[field] = {};
                        processFields(fieldData[field], data[field], updateData[field], withinArray);
                    }
                }
            });
        };

        processFields((dateFields as any)[collection], document, updateData, false);

        if (updates > 0) {
            // Update document
            // console.log(`>>> updateData`, updateData);
            return firestore.collection(collection).doc(document.id).set(updateData, { merge: true }).then(() => {
                if (results.datesConformed[collection] === undefined) {
                    results.datesConformed[collection] = 0;
                }
                results.datesConformed[collection]++;
            });
        }

        return Promise.resolve();
    };

    const processCustomForms = () => {
        // Gather custom form versions
        results.datesConformed.customFormsCompleted = 0;
        const customFormVersionsWithDates = [] as any[];
        
        return protectFirestoreOperation(
            `Get all customFormVersions`,
            () => firestore.collection('customFormVersions').get()
        ).then((snap: QuerySnapshot<DocumentData, DocumentData>) => {
            console.log(`[] customFormVersions (${snap.docs.length})`);
            snap.docs.forEach((doc) => {
                const customFormVersion = {
                    id: doc.id,
                    ...doc.data()
                } as any;
                let includesDateElement = false;
                if (customFormVersion.form) {
                    Object.values(customFormVersion.form).forEach((element: any) => {
                        if (element?.id === 'date') {
                            includesDateElement = true;
                        }
                    });
                }
                if (includesDateElement) {
                    customFormVersionsWithDates.push(customFormVersion);
                }
            });
            console.log(`customFormVersions with dates: ${customFormVersionsWithDates.length}`);
            return processTasks(
                customFormVersionsWithDates,
                (customFormVersion: any) => {
                    const dateElementKeys = [] as string[];
                    Object.keys(customFormVersion.form).forEach((key) => {
                        if (customFormVersion.form[key].id === 'date') {
                            dateElementKeys.push(key);
                        }
                    });
                    return firestore.collection('customFormsCompleted')
                    .where('customFormId', '==', customFormVersion.customFormId)
                    .where('version', '==', customFormVersion.version)
                    .get()
                    .then((snap) => {
                        const completedForms = snap.docs.map((doc) => {
                            return {
                                id: doc.id,
                                ...doc.data()
                            } as any;
                        });
                        //console.log(`matched ${snap.docs.length} customFormsCompleted`);
                        //console.log(`dateElementKeys (${dateElementKeys.length})`, dateElementKeys);
                        return processTasks(
                            completedForms,
                            (completedForm) => {
                                const updateData = {} as any;
                                dateElementKeys.forEach((key) => {
                                    if (completedForm.data && completedForm.data[key] && typeof completedForm.data[key] === 'number') {
                                        let dateTime = makeDateTime(completedForm.data[key]);
                                        if (completedForm.data[key] < 0) {
                                            negatives++;
                                            dateTime = dateTime.set({ year: dateTime.get('year')+2000 });
                                        }
                                        updateData[key] = dateTime.toISODate();
                                    }
                                });
                                if (Object.keys(updateData).length) {
                                    //console.log(`updateData`, updateData);
                                    return firestore.collection('customFormsCompleted').doc(completedForm.id).set({
                                        data: {
                                            ...updateData
                                        }
                                    }, { merge: true }).then(() => {
                                        results.datesConformed.customFormsCompleted++;
                                    });
                                }
                                return Promise.resolve();
                            }
                        );
                    });
                }, {
                    logProgressEvery: 100,
                    itemsName: 'customFormVersions with dates'
                }
            );
        });
    };

    const whenStarted = Date.now();
    return processTasks(
        collections, //['drills'],
        processCollection
    ).then(() => {
        return processCustomForms();
    }).then(() => {
        const timesCountsList = Object.values(timeCounts);
        timesCountsList.sort((a: any, b: any) => {
            return b.count - a.count;
        });
        let s = `NaN: ${nanCount}\n`;
        s += `Zeroes: ${zeroes}\n`;
        s += `Negatives: ${negatives}\n`;
        s += `Before 1900: ${before1900}\n`;
        s += `After 2200: ${after2200}\n`;
        s += '\n';

        console.log(`stats`, s);
        console.log(`conformAllDates took ${formatTimeDuration(Date.now() - whenStarted)})`);

        timesCountsList.forEach((o: any) => {
            s += `${o.time} (${o.count}) eg. ${o.example}\n`;
        });
        return sendMail({
            from: settings.fromSeaFluxEmail,
            to: `Ben Robinson <<EMAIL>>`,
            subject: `Dates Count`,
            text: s,
        });
    });
};









/**
 * Gather all usage of when* fields so I can make sure we're not missing any datesFields
 */
const gatherWhenFields = (results: any) => {
    return traverseFirestoreStructure(
        100000,
        1, // Not implemented yet
        (collection: string) => (
            collection.startsWith('_') ||
            collection === 'errorReports' ||
            collection === 'traceReports' ||
            collection === 'overdueStats' ||
            collection === 'userTimezones'
        ) ? false : true,
        (path: string) => (
            path.startsWith('drills.') ||
            path.startsWith('crew.') ||
            path.startsWith('form.e') ||
            path.startsWith('data.') ||
            path.startsWith('hours.') ||
            path.endsWith('sfdoc')
        ) ? false : true,
        (field: string) => field.startsWith('when')
    ).then((structure: any) => {
        console.log(`>>> structure`, structure);
        results.structure = structure;
        return sendMail({
            from: settings.fromSeaFluxEmail, // If sending to crew, perhaps this should the licensee's email?
            to: `Ben Robinson <<EMAIL>>`,
            subject: `Firestore structure`,
            text: JSON.stringify(structure, undefined, '    '),
        });
    });
};

const traverseFirestoreStructure = (
    sampleSize = 1000, // What are the maximum number of documents to inspect per collection? 
    countRequired = 1, // How many times should a field be found to bother including it in structure? (not implemented!)
    includeCollection: (collection: string) => boolean, // Function to determine if collection should be included
    includePath: (path: string) => boolean,
    includeField: (field: string) => boolean // Function to determine if field should be included
) => {
    const structure = {} as any;

    const processCollection = (collection: string) => {
        if (!includeCollection(collection)) {
            return Promise.resolve(); // collection rejected
        }
        console.log(`[] ${collection}`);
        return firestore.collection(collection).limit(sampleSize).get().then((snap) => {
            const o = {} as any;
            const processDocument = (data: any, path: string) => {
                if (data == undefined || data === null || !includePath(path)) {
                    return;
                }
                if (Array.isArray(data)) {
                    data.forEach((value) => {
                        processDocument(value, `${path}[]`);
                    });
                } else if (typeof data === 'object') {
                    Object.keys(data).sort().forEach((field) => {
                        processDocument(data[field], `${path ? `${path}.` : ''}${field}`);
                    });
                } else if (data !== undefined && data !== null) {
                    // regular field
                    const field = path.split('.')[path.split('.').length - 1];
                    if (!includeField(field)) {
                        return;
                    }
                    if (o[path] === undefined) {
                        o[path] = 0;
                    }
                    o[path]++;
                }
            };
            snap.docs.forEach((doc) => {
                processDocument(doc.data(), '');
            });
            // Sort keys...
            const o2 = {} as any;
            Object.keys(o).sort().forEach((key) => {
                o2[key] = o[key];
            });
            structure[collection] = o2;
        });
    };

    return firestore.listCollections().then((collectionsList) => {
        const collections = collectionsList.map(data => data.id);
        console.log(`collections`, collections);
        // const collections = collections.map(col => col.id);
        // collectionExists = collectionNames.includes('crewCertificateTitles');
        return processTasks(
            collections,
            processCollection
        );
    }).then(() => {
        return structure;
    })
};
