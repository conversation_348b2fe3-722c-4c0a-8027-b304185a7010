import { firestore } from "../init";
import { protectFirestoreOperation, requireSuperAdmin } from "../lib/util_firebase";
import { defaultEventHandlerOptionsForHttps, maxTimeoutSeconds, defaultEventHandlerOptions, maxMemoryFor4Cpu } from "../eventHandlerOptions";
import { onCall } from "firebase-functions/https";
import { onDocumentCreated } from "firebase-functions/firestore";
import { fixBadCustomFormsFileRefs } from "./adminDoMaintenance-tasks/fixBadCustomFormsFileRefs";
import { fixBadVesselReferences } from "./adminDoMaintenance-tasks/fixBadVesselReferences";
import { fixNonActiveVessels } from "./adminDoMaintenance-tasks/fixNonActiveVessels";
import { maintainCustomFormTemplates } from "../custom-forms/maintainCustomFormTemplates";
import { fixCustomFormForVesselIds } from "../custom-forms/fixCustomFormForVesselIds";
import { fixPermissionInconsistencies } from "../users-and-security/fixPermissionInconsistencies";
import { conformAllDates } from "./adminDoMaintenance-tasks/conformAllDates";
import { fixScheduledMaintenanceTasksWithNoEquipment } from "./adminDoMaintenance-tasks/fixDeletedScheduledMaintenanceTasks";


/**
 * Does general maintenance.
 * Intended to run along side deployments and monthly audits.
 * 
 * Note: Some functions this calls will become obsolete over time as issues are completely resolved.
 */
export const adminDoMaintenance = onCall({
    ...defaultEventHandlerOptionsForHttps,
    cpu: 4,
    memory: maxMemoryFor4Cpu
}, (request, response) => {
    console.log('adminDoMaintenance!');
    const results = {} as any;
    return requireSuperAdmin(request).then(() => {
        return conformAllDates(results);
    }).then(() => {
        return fixBadCustomFormsFileRefs(results);
    }).then(() => {
        return fixBadCustomFormsFileRefs(results);
    }).then(() => {
        return fixBadVesselReferences(results);
    }).then(() => {
        return fixNonActiveVessels(results);
    }).then(() => {
        return fixCustomFormForVesselIds(results);
    }).then(() => {
        return maintainCustomFormTemplates(results);
    }).then(() => {
        return fixScheduledMaintenanceTasksWithNoEquipment(results);
    }).then(() => {
        console.log('Starting adminDoMaintenanceTrigger...');
        return protectFirestoreOperation(
            'add to _adminDoMaintenanceTrigger',
            () => firestore.collection('_adminDoMaintenanceTrigger').add({
                cmd: 'fixPermissionInconsistencies',
                phase: 1,
                totalPhases: 8
            })
        );
    }).then(() => {
        console.log('RESULTS', results);
        return results;
    });
});



/**
 * Augments adminDoMaintenance so it can run over multiple sequential calls.
 */
export const adminDoMaintenanceTrigger = onDocumentCreated(
    {
        ...defaultEventHandlerOptions,
        cpu: 4,
        memory: maxMemoryFor4Cpu,
        timeoutSeconds: maxTimeoutSeconds,
        document: '_adminDoMaintenanceTrigger/{id}'
    },
    (event) => {
        const data = {
            ...event.data!.data(),
            id: event.data!.id
        } as any;
        console.log(`adminDoMaintenanceTrigger id=${data.id} data`, data);
        const results = {} as any;
        return Promise.resolve().then(() => {
            if (data.cmd === 'fixPermissionInconsistencies') {
                return fixPermissionInconsistencies(data.phase, data.totalPhases, results).then(() => {
                    // Kick start next phase
                    return protectFirestoreOperation(
                        'add to _adminDoMaintenanceTrigger',
                        () => firestore.collection('_adminDoMaintenanceTrigger').add(
                            (data.phase >= data.totalPhases) ?
                            {
                                cmd: 'somethingElse'
                            } : {
                                cmd: 'fixPermissionInconsistencies',
                                phase: data.phase + 1,
                                totalPhases: data.totalPhases
                            }
                        )
                    );
                });
            } else if (data.cmd === 'somethingElse') {
                //return migrateFilesToNewSystemPrepare(firestore, admin, settings, data.phase, results);
            }
            console.log('Maintenance trigger not recognised!');
            return Promise.resolve();
        }).then(() => {
            console.log('Deleting _adminDoMaintenanceTrigger '+data.id);
            return firestore.collection('_adminDoMaintenanceTrigger').doc(data.id).delete();
        }).then(() => {
            console.log('RESULTS', results);
            return Promise.resolve(results);
        });
    }
);

