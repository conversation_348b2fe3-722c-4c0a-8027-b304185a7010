import { DocumentData, DocumentSnapshot, QuerySnapshot } from "firebase-admin/firestore";
import { firestore } from "../init";
import { formatDatetime } from "../lib/datesAndTime";
import { protectFirestoreOperation, requireSuperAdmin } from "../lib/util_firebase";
import { onSchedule } from "firebase-functions/scheduler";
import { defaultEventHandlerOptionsForScheduled, maxMemoryFor2Cpu } from "../eventHandlerOptions";
import { onCall } from "firebase-functions/https";
import { settings } from "../projectSettings";
import { sendMail } from "../lib/email";

/**
 * Processes traceReports and sends an email to settings.errorReportsTo
 */
export const scheduledProcessTraceReports = onSchedule(
    {
        ...defaultEventHandlerOptionsForScheduled,
        cpu: 1,
        memory: '2GiB',
        schedule: 'every mon,tue,wed,thu,fri 18:00' // Run every day at 4am NZST
    },
    (event) => {
        const results = {} as any;
        return processTraceReports(
            results,
            4 * 60 * 60 // Only look at reports older than 4 hours
        ).then(() => {
            console.log('RESULTS', results);
        });
    }
);

/**
 * Used to manually run processTraceReports.
 */
export const adminProcessTraceReports = onCall({
        ...defaultEventHandlerOptionsForScheduled,
        cpu: 2,
        memory: maxMemoryFor2Cpu
    }, (request, response) => {
        const results = {} as any;
        return requireSuperAdmin(request).then(() => {
            return processTraceReports(
                results,
                1 * 60 // Only look at reports older than 1 minute
            );
        }).then(() => {
            console.log('RESULTS', results);
            return results;
        });
    }
);


const processTraceReports = (results: any, minSecondsAgo: number): Promise<any> => {
    console.log('processTraceReports v27');
    results.types = {} as any;
    results.collections = {} as any;
    results.numProcessed = 0;
    const traceReports = [] as any[];
    let emailContent = '';

    const processReports = (): Promise<void> => {
        if (traceReports.length === 0) {
            return Promise.resolve();
        }
        const promises = [] as Promise<void>[];
        for (let i = 0; i < 20; i++) {
            if (traceReports.length === 0) {
                break;
            }
            const traceReport = traceReports.shift();
            results.numProcessed++;
            if (traceReport.collection) {
                if (results.collections[traceReport.collection] === undefined) {
                    results.collections[traceReport.collection] = 0;
                }
                results.collections[traceReport.collection]++;
            } else {
                console.error('traceReport is missing collection!', traceReport);
            }
            if (traceReport.type) {
                if (results.types[traceReport.type] === undefined) {
                    results.types[traceReport.type] = 0;
                }
                results.types[traceReport.type]++;
            } else {
                console.error('traceReport is missing type!', traceReport);
            }

            // Checks
            // 1. does document exist? ...only really helps for creates
            // 2. is confirmed? ...if batches, writeCount >= batches. Otherwise > 0
            // Could just check via actionsConfirmed...
            // 3. check if has errorReport

            promises.push(
                Promise.resolve().then(() => {
                    let errorReport: any;
                    let actionConfirmed: any;
                    let state: string;
                    return protectFirestoreOperation(
                        `get traceReport ${traceReport.id}`,
                        () => firestore.collection('errorReports').doc(traceReport.errorReportId).get()
                    ).then((doc: DocumentSnapshot<DocumentData, DocumentData>) => {
                        if (doc.exists) {
                            errorReport = doc.data();
                        }
                        return protectFirestoreOperation(
                            `get actionsConfirmed ${traceReport.errorReportId}`,
                            () => firestore.collection('actionsConfirmed').doc(traceReport.errorReportId).get()
                        );
                    }).then((doc: DocumentSnapshot<DocumentData, DocumentData>) => {
                        if (doc.exists) {
                            actionConfirmed = doc.data();
                        }
                        state = 'success';
                        if (
                            actionConfirmed && (
                                (actionConfirmed.batches === undefined && actionConfirmed.writeCount > 0) ||
                                (actionConfirmed.batches !== undefined && actionConfirmed.writeCount >= actionConfirmed.batches)
                            )
                        ) { // Action succeeded
                            if (errorReport) {
                                // Fake error report
                                if (errorReport.shownToUser !== undefined) {
                                    state = errorReport.shownToUser ? 'fakeErrorSeen' : 'fakeErrorUnseen';
                                } else {
                                    state = 'fakeError'; // Error report generated when it didn't need to
                                }
                            }
                        } else { // Action failed
                            if (errorReport) {
                                if (errorReport.shownToUser !== undefined) {
                                    state = errorReport.shownToUser ? 'errorSeen' : 'errorUnseen';
                                } else {
                                    state = 'error'; // Error report for a genuine error
                                }
                            } else {
                                state = 'errorLost'; // Error happened without an error report
                            }
                        }
                        return protectFirestoreOperation(
                            `set traceReport ${traceReport.id}`,
                            () => firestore.collection('traceReports').doc(traceReport.id).set({
                                state: state
                            }, {merge: true})
                        );
                    }).then(() => {
                        if (state !== 'success') {
                            console.log(`>>> state=${state}`, traceReport);
                            let userName: string;
                            let licenseeName: string;
                            return Promise.resolve().then(() => {
                                if (traceReport.userId && traceReport.userId !== 'unknown') {
                                    return protectFirestoreOperation(
                                        `get user ${traceReport.userId}`,
                                        () => firestore.collection('users').doc(traceReport.userId).get()
                                    ).then((doc: DocumentSnapshot<DocumentData, DocumentData>) => {
                                        if (doc.exists) {
                                            return Promise.resolve(`${doc.data()!.firstName} ${doc.data()!.lastName}`);
                                        }
                                        return Promise.resolve('unknown');
                                    });
                                }
                                return Promise.resolve('unknown');
                            }).then((_userName) => {
                                userName = _userName;
                                if (traceReport.licenseeId) {
                                    return protectFirestoreOperation(
                                        `get user ${traceReport.licenseeId}`,
                                        () => firestore.collection('users').doc(traceReport.licenseeId).get()
                                    ).then((doc: DocumentSnapshot<DocumentData, DocumentData>) => {
                                        if (doc.exists) {
                                            return Promise.resolve(`${doc.data()!.firstName} ${doc.data()!.lastName}`);
                                        }
                                        return Promise.resolve('unknown');
                                    });
                                }
                                return Promise.resolve('unknown');
                            }).then((_licenseeName) => {
                                licenseeName = _licenseeName;
                                emailContent += `
                                    ${state} (${formatDatetime(traceReport.whenAction)})
                                    Action: ${traceReport.action}
                                    Synced: ${formatDatetime(traceReport.whenSynced._seconds * 1000)}
                                    User: ${userName} (${traceReport.userId})
                                    Licensee: ${licenseeName} (${traceReport.licenseeId})
                                    Document: ${traceReport.type} ${traceReport.collection} ${traceReport.docId}
                                    App: ${traceReport.version} ${traceReport.build} device=${traceReport.deviceId}
                                    Report Id: ${traceReport.id}
                                    Batch writes: ${actionConfirmed ? `${actionConfirmed.writeCount}/${actionConfirmed.batches !== undefined ? actionConfirmed.batches : '?'}` : 'None'}
                                    \n
                                `;
                            });
                        }
                        return;
                    });
                })
            );

        }
        return Promise.all(promises).then(() => {
            return processReports();
        });
    };

    return protectFirestoreOperation(
        `get traceReports`,
        () => firestore.collection('traceReports')
        .where('state', '==', 'unknown')
        .where('whenSynced', '<', new Date(Date.now() - (minSecondsAgo * 1000)))
        .orderBy('whenSynced', 'desc')
        .get()
    ).then((snap: QuerySnapshot<DocumentData, DocumentData>) => {
        snap.docs.forEach((doc) => {
            traceReports.push({
                id: doc.id,
                ...doc.data()
            });
        });
        return processReports();
    }).then(() => {
        if (emailContent.length > 0) {
            console.log(`Sending an email to ${settings.errorReportsTo}`);
            return sendMail({
                from: settings.fromSeaFluxEmail,
                to: settings.errorReportsTo,
                subject: `Traced Errors${ settings.env === 'production' ? '' : ` (${settings.env})` }`,
                text: emailContent,
            });
        }
        return Promise.resolve(undefined);
    });
};
