import { DocumentData, FieldValue, QuerySnapshot } from "firebase-admin/firestore";
import { firestore } from "../../init";
import { createDefaultUserPermissionDefaults } from "../tasks/createDefaultUserPermissionDefaults";
import { fixUserRoleIds } from "../tasks/fixUserRoleIds";
import { migrateDangerousGoodVesselIds } from "../tasks/migrateDangerousGoodVesselIds";
import { processTasks, protectFirestoreOperation } from "../../lib/util_firebase";

/**
 * Mandatory release.
 * - Added gap analysis to crew certificates.
 * - Added auto-saving to logbook voyage progress.
 * - New permission levels have been added for more flexibility.
 * - You can now choose how Sea Flux caches files on your device.
 * - Added "assigned to" to safety checks.
 * - Custom text fields can now be filled in for multi-trip voyages.
 * - You can now create a maintenance task while viewing equipment.
 * - Dangerous goods can be associated with more than one vessel.
 * - Vessel certificates can now be categorised.
 * 
 * Note: There may some function calls missing here that were actually called during the 1.5.0 migration
 */
export const version_1_5_0 = (results: any) => {
    return createDefaultUserPermissionDefaults(results).then(() => {
        return fixUserRoleIds(results);
    }).then(() => {
        return migrateDangerousGoodVesselIds(results);
    }).then(() => {
        updatePermissionLevels(results);
    }).then(() => {
        updateLicenseeSettings(results);
    });
};


const updatePermissionLevels = (results: any) => {
    console.log('>>> updatePermissionLevels!');
    results.userPermissionsUpdated = 0;

    return firestore.collection('userPermissions').get().then((snap) => {
        const userPermissionsToUpdate = [] as any[];
        snap.docs.forEach((doc) => {
            let userPermission = doc.data();
            if (Object.values(userPermission).includes(4) || Object.values(userPermission).includes(6)) {
                const updatedPermission = {} as any;
                Object.keys(userPermission).forEach(key => {
                    if (userPermission[key] === 4) {
                        updatedPermission[key] = 7;
                    } else if (userPermission[key] === 6) {
                        if (['vesselSettings', 'engineHours', 'companyPlan'].includes(key)) {
                            updatedPermission[key] = 7;
                        } else {
                            updatedPermission[key] = 9;
                        }
                    }
                });
                userPermissionsToUpdate.push({
                    id: doc.id,
                    ...updatedPermission
                });
            }
        });

        console.log(`userPermissionsToUpdate.length=${userPermissionsToUpdate.length}`);

        // Process userPermissionsToUpdate
        return processTasks(
            userPermissionsToUpdate,
            ((userPermission) => {
                const { id, ...permissions } = userPermission;
                return firestore.collection('userPermissions').doc(id).set({
                    ...permissions,
                    id: FieldValue.delete()
                }, { merge: true }).then(() => {
                    results.userPermissionsUpdated++;
                    return Promise.resolve();
                }).catch((error) => {
                    console.error('Error updatePermissionLevels', error);
                    results.userPermissionsUnupdated++;
                    return Promise.resolve();
                });
            }),
            {
                batchSize: 100,
                logProgressEvery: 100,
                itemsName: 'userPermissions'
            }
        );
    });
};

const updateLicenseeSettings = (results: any) => {
    console.log('>>> Batch updating licenseeSettings with new fields!');
    results.licenseeSettingsUpdated = 0;
    results.licenseeSettingsUpdateFailures = 0;

    return protectFirestoreOperation(
        'getting licenseeSettings to update',
        () => firestore.collection('licenseeSettings').get()
    ).then((snap: QuerySnapshot<DocumentData, DocumentData>) => {
        const licenseeSettingsToUpdate = [] as any[];
        snap.docs.forEach((doc) => {
            if (doc.data().hasIncidents === undefined) {
                licenseeSettingsToUpdate.push({
                    id: doc.id,
                    hasIncidents: true,
                    hasReporting: true
                });
            }
        });
        return processTasks(
            licenseeSettingsToUpdate,
            (licenseeSetting) => {
                return firestore.collection('licenseeSettings').doc(licenseeSetting.id).set({
                    hasIncidents: licenseeSetting.hasIncidents,
                    hasReporting: licenseeSetting.hasReporting
                }, { merge: true }).then(() => {
                    results.licenseeSettingsUpdated++;
                }).catch((error) => {
                    console.error('Error updating licenseeSettings', error);
                    results.licenseeSettingsUpdateFailures++;
                });
            }, {
                batchSize: 100,
                logProgressEvery: 100,
                itemsName: 'licenseeSettings'
            }
        );
    });
};
