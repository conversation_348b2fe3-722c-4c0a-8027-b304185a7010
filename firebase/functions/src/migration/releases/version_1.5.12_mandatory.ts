import { firestore } from "../../init";
import { fixUserRoleIds } from "../tasks/fixUserRoleIds";
import { processTasks, protectFirestoreOperation } from "../../lib/util_firebase";
import { DocumentData, QuerySnapshot } from "firebase-admin/firestore";

/**
 * This is the release where we upgraded Firebase Functions to v2
 * 
 * Mandatory release.
 * - You can now specify which job and crew certificate notifications to a more granular level.
 * - Dates (without a time specified) are now yyyy-MM-dd strings and are all named like date* e.g. dateDue instead of whenDue
 * 
 */
export const version_1_5_12 = (results: any) => {
    return initMissingTimeZones(results).then(() => {
        return addCrewCertificatesMeOnlyEmailMeSettings(results);
    }).then(() => {
        return fixUserRoleIds(results);
    }).then(() => {
        return addJobsEmailMeSettings(results);
    });
};


const addCrewCertificatesMeOnlyEmailMeSettings = (results: any) => {
    results.crewCertificatesMeOnlyEmailMeSettingsAdded = 0;
    console.log(`addCrewCertificatesMeOnlyEmailMeSettings!`);
    const usersToUpdate = [] as any[];

    return firestore.collection('userDetails').get().then((snap) => {
        snap.docs.forEach((doc) => {
            const userDetails = doc.data();
            if (!userDetails.emailMe) {
                userDetails.emailMe = [];
            }
            if (userDetails.emailMe && userDetails.emailMe.indexOf('crewCertificates') === -1 && userDetails.emailMe.indexOf('crewCertificatesMeOnly') === -1) {
                userDetails.emailMe.push('crewCertificatesMeOnly');
                usersToUpdate.push({
                    id: doc.id,
                    emailMe: userDetails.emailMe
                });
            }
        });

        console.log(`>>> ${usersToUpdate.length}/${snap.docs.length} userDetails need to have crewCertificatesMeOnly added...`);
        return processTasks(
            usersToUpdate,
            (userDetails) => {
                return protectFirestoreOperation(
                    `Update userDetails.emailMe`,
                    () => firestore.collection('userDetails').doc(userDetails.id).set({
                        emailMe: userDetails.emailMe
                    }, { merge: true })
                ).then(() => {
                    results.crewCertificatesMeOnlyEmailMeSettingsAdded++;
                });
            }, {
                batchSize: 10,
                logProgressEvery: 100,
                itemsName: 'userDetails to update emailMe'
            }
        );
    });
};


const addJobsEmailMeSettings = (results: any) => {
    results.jobsEmailMeSettingsAdded = 0;
    console.log(`addJobsEmailMeSettings!`);
    const usersToUpdate = [] as any[];
    const permissionsToAdd = ['jobsUpdated', 'jobsUrgent', 'jobsHigh', 'jobsMedium', 'jobsLow', 'jobsShipyard'];

    return firestore.collection('userDetails').get().then((snap) => {
        snap.docs.forEach((doc) => {
            const userDetails = doc.data();
            if (!userDetails.emailMe) {
                userDetails.emailMe = [];
            }
            if (userDetails.emailMe.includes('jobs')) {
                const updatedEmailMe = userDetails.emailMe.filter(
                    (permission: string) => permission !== 'jobs'
                );
                usersToUpdate.push({
                    id: doc.id,
                    emailMe: [...updatedEmailMe, ...permissionsToAdd]
                });
            }
        });

        console.log(`>>> ${usersToUpdate.length}/${snap.docs.length} userDetails need to have jobs emailMe settings added...`);
        return processTasks(
            usersToUpdate,
            (userDetails) => {
                return protectFirestoreOperation(
                    `Update userDetails.emailMe`,
                    () => firestore.collection('userDetails').doc(userDetails.id).set({
                        emailMe: userDetails.emailMe
                    }, { merge: true })
                ).then(() => {
                    results.jobsEmailMeSettingsAdded++;
                });
            }, {
                batchSize: 10,
                logProgressEvery: 100,
                itemsName: 'userDetails to update emailMe'
            }
        );
    });
};


const initMissingTimeZones = (results: any) => {
    console.log(`initMissingTimeZones!`);
    results.timeZones = {
        totalInitialised: 0
    };

    const manualTimeZones = {
        'I2UFU8er1vi10jcTZFSk': 'Pacific/Auckland',
        'l0z4lKn6zPTNjr4JmTpW': 'Pacific/Fiji',
        'jXCVpP0lZHWjAG4uHdLg': 'Pacific/Auckland',
        'sn6Cjl3neTMymdnRM68I': 'Pacific/Fiji',
        'CY2TakveXZ15IlKxXNVa': 'Europe/Paris',
        'EGuNnCOK3VM40IX4KXAM': 'Pacific/Tarawa',
        'HIPkcMkIaFiSnwy0VZpD': 'Europe/Berlin',
        '2qgyLY7c8AdEUvbyrSTb': 'Europe/Paris',
        'uQ3U2ORm9w5DuQToJ3s5': 'Europe/Paris',
        'VjooruwxIQV24yBM4aia': 'Pacific/Auckland',
        'EqeWf62YU1vLmmoxdQvI': 'Asia/Dubai',
        '34cXyfuvLiLX5qpJSfwz': 'Asia/Dubai',
        'uH4soZnPrIFpgvyBhtFU': 'Pacific/Auckland',
        'SWRZsOnz1eXSryoAL3Yy': 'Europe/Paris',
    } as any;

    return protectFirestoreOperation(
        'Get all licenseeSettings',
        () => firestore.collection('licenseeSettings').get()
    ).then((snap: QuerySnapshot<DocumentData, DocumentData>) => {
        const settingsToUpdate = [] as any[];
        snap.docs.forEach((doc) => {
            const settings = {
                id: doc.id,
                ...doc.data()
            } as any;
            if (settings.timeZone === undefined) {
                settingsToUpdate.push(settings);
            }
        });
        console.log(`settingsToUpdate.length=${settingsToUpdate.length}`);
        return processTasks(
            settingsToUpdate,
            (licenseeSettings) => {
                let timeZone = '';
                switch (licenseeSettings.region) {
                    case 'nz':
                        timeZone = 'Pacific/Auckland';
                        break;
                    case 'au':
                        timeZone = 'Australia/Brisbane';
                        break;
                    case 'uk':
                        timeZone = 'Europe/London';
                        break;
                    case 'default':
                        if (manualTimeZones[licenseeSettings.id]) {
                            timeZone = manualTimeZones[licenseeSettings.id];
                        }
                        break;
                    default:
                        console.error(`Not handling region=${licenseeSettings.region}!`);
                }
                if (timeZone === '') {
                    console.log(`No timeZone found for ${licenseeSettings.id} region=${licenseeSettings.region}! Using Pacific/Auckland`);
                    timeZone = 'Pacific/Auckland';
                }
                return firestore.collection('licenseeSettings').doc(licenseeSettings.id).set({
                    timeZone: timeZone
                }, { merge: true }).then(() => {
                    results.timeZones.totalInitialised++;
                    if (results.timeZones[timeZone] === undefined) {
                        results.timeZones[timeZone] = 0;
                    }
                    results.timeZones[timeZone]++;
                });
            }
        );
    });
};
