import { FieldValue } from "firebase-admin/firestore";
import { firestore } from "../../init";
import { processTasks, protectFirestoreOperation } from "../../lib/util_firebase";

/**
 * 
 */
export const version_1_5_13 = (results: any) => {
    return initCorrectiveActionPermissions(results).then(() => {
        return addJobsCompletedEmailMeSettings(results);
    });
};

const initCorrectiveActionPermissions = (results: any) => {
    results.correctiveActionPermissionsInitialised = 0;
    results.correctiveActionPermissionsIdFieldRemoved = 0;
    results.correctiveActionPermissionDefaultsInitialised = 0;
    results.correctiveActionLicenseeSettingsInitialised = 0;
    console.log(`initCorrectiveActionPermissions!`);

    return firestore.collection('userPermissions').get().then((snap) => {
        const usersToUpdate = [] as any[];
        const userPermissionsToRemoveIds = [] as any[];
        snap.docs.forEach((doc) => {
            // id is added after as there was an incorrect id assigned to some userPermissions docs
            const userPermission = {
                ...doc.data(),
                id: doc.id
            } as any;
            if (doc.data().id && userPermission.id != doc.data().id) {
                userPermissionsToRemoveIds.push(userPermission.id);
            }
            if (userPermission.correctiveActions === undefined) {
                usersToUpdate.push(userPermission);
            }
        });
        console.log(`>>> ${usersToUpdate.length}/${snap.docs.length} users need to have correctiveActionPermissions added...`);
        console.log(`>>> ${userPermissionsToRemoveIds.length} userPermissions need to have id field removed...`);
        return processTasks(
            usersToUpdate,
            (userPermission) => {
                return protectFirestoreOperation(
                    `Update userPermissions.correctiveActions`,
                    () => firestore.collection('userPermissions').doc(userPermission.id).set({
                        correctiveActions: userPermission.incidentAccidentMedicalRegister || 0
                    }, { merge: true })
                ).then(() => {
                    results.correctiveActionPermissionsInitialised++;
                });
            }, {
                batchSize: 10,
                logProgressEvery: 100,
                itemsName: 'users to init correctiveActionPermissions'
            }
        ).then(() => {
            return processTasks(
                userPermissionsToRemoveIds,
                (id) => {
                    return protectFirestoreOperation(
                        `Remove userPermissions.id field`,
                        () => firestore.collection('userPermissions').doc(id).update({
                            id: FieldValue.delete()
                        })
                    ).then(() => {
                        results.correctiveActionPermissionsIdFieldRemoved++;
                    });
                }, {
                    batchSize: 10,
                    logProgressEvery: 100,
                    itemsName: 'userPermissions to remove id field'
                }
            );
        });
    }).then(() => {
         // Update userPermissionDefaults collection
         return firestore.collection('userPermissionDefaults').get().then((snap) => {
            const defaultsToUpdate = [] as any[];
            snap.docs.forEach((doc) => {
                const defaultPermission = {
                    id: doc.id,
                    ...doc.data()
                } as any;
                if (defaultPermission.correctiveActions === undefined) {
                    defaultsToUpdate.push(defaultPermission);
                }
            });
            console.log(`>>> ${defaultsToUpdate.length}/${snap.docs.length} defaults need to have correctiveActionPermissions added...`);
            return processTasks(
                defaultsToUpdate,
                (defaultPermission) => {
                    return protectFirestoreOperation(
                        `Update userPermissionDefaults.correctiveActions`,
                        () => firestore.collection('userPermissionDefaults').doc(defaultPermission.id).set({
                            correctiveActions: defaultPermission.incidentAccidentMedicalRegister || 0
                        }, { merge: true })
                    ).then(() => {
                        results.correctiveActionPermissionDefaultsInitialised++;
                    });
                }, {
                    batchSize: 10,
                    logProgressEvery: 100,
                    itemsName: 'defaults to init correctiveActionPermissions'
                }
            );
        });
    }).then(() => {
        // Update licenseeSettings collection
        return firestore.collection('licenseeSettings').get().then((snap) => {
            const settingsToUpdate = [] as any[];
            snap.docs.forEach((doc) => {
                const licenseeSetting = {
                    id: doc.id,
                    ...doc.data()
                } as any;
                if (licenseeSetting.hasCorrectiveActions === undefined) {
                    console.log(`>>> ${licenseeSetting.id} has no hasCorrectiveActions`);
                    results.correctiveActionLicenseeSettingsInitialised++;
                    settingsToUpdate.push(licenseeSetting);
                }
            });
            console.log(`>>> ${settingsToUpdate.length}/${snap.docs.length} licensee settings need to have hasCorrectiveActions added...`);
            return processTasks(
                settingsToUpdate,
                (licenseeSetting) => {
                    return protectFirestoreOperation(
                        `Update licenseeSettings.hasCorrectiveActions`,
                        () => firestore.collection('licenseeSettings').doc(licenseeSetting.id).set({
                            hasCorrectiveActions: false
                        }, { merge: true })
                    );
                }, {
                    batchSize: 10,
                    logProgressEvery: 100,
                    itemsName: 'licensee settings to init hasCorrectiveActions'
                }
            );
        });
    });
};

const addJobsCompletedEmailMeSettings = (results: any) => {
    results.jobsCompletedEmailMeSettingsAdded = 0;
    console.log(`addJobsCompletedEmailMeSettings!`);
    const usersToUpdate = [] as any[];

    return firestore.collection('users').get().then((snap) => {
        snap.docs.forEach((doc) => {
            const user = doc.data();
            if (!user.emailMe) {
                user.emailMe = [];
            }
            // If the user has jobsUpdated or jobsCreated, but not jobsCompletedCreated or jobsCompletedUpdated, add jobsCompletedCreated
            if ((!user.emailMe.includes('jobsCompletedCreated') && !user.emailMe.includes('jobsCompletedUpdated')) && (
                user.emailMe.includes('jobs') || user.emailMe.includes('jobsUpdated') || user.emailMe.includes('jobsCreated')
            )) {
                const permissionsToExclude = new Set(['jobs', 'jobsCompletedCreated', 'jobsCompletedUpdated']);
                const updatedEmailMe = user.emailMe.filter(
                    (permission: string) => !permissionsToExclude.has(permission)
                );
                usersToUpdate.push({
                    id: doc.id,
                    emailMe: [...updatedEmailMe, 'jobsCompletedCreated']
                });
            }
        });

        console.log(`>>> ${usersToUpdate.length}/${snap.docs.length} users need to have jobsCompletedCreated emailMe settings added...`);
        return processTasks(
            usersToUpdate,
            (user) => {
                return protectFirestoreOperation(
                    `Update user.emailMe`,
                    () => firestore.collection('users').doc(user.id).set({
                        emailMe: user.emailMe
                    }, { merge: true })
                ).then(() => {
                    results.jobsCompletedEmailMeSettingsAdded++;
                });
            }, {
                batchSize: 10,
                logProgressEvery: 100,
                itemsName: 'users to update emailMe'
            }
        );
    });
};
