import { DocumentData, FieldValue, QuerySnapshot } from "firebase-admin/firestore";
import { firestore } from "../../init";
import { protectFirestoreOperation } from "../../lib/util_firebase";


const migrateSopTypesToCategories = (results: any) => {
    console.log('migrateSopTypesToCategories!');
    results.sopCategoriesCount = 0;
    results.sopCategoriesDeleted = 0;
    results.sopCategoriesCreated = 0;
    results.sopsMigrated = 0;
    const sopCategories = [] as any[];
    const operationalProcedures = {} as any;
    const emergencies = {} as any;
    const companyPolicies = {} as any;
    const vessels = [] as any[];
    const sops = [] as any[];
    const processSopCategories = (): Promise<void> => {
        if (sopCategories.length === 0) {
            return Promise.resolve();
        }
        const sopCategory = sopCategories.shift();
        results.sopCategoriesCount++;
        switch (sopCategory.name.trim().toLowerCase()) {
            case 'operational procedures':
                operationalProcedures[sopCategory.vesselId] = sopCategory.id;
                break;
            case 'emergency':
                emergencies[sopCategory.vesselId] = sopCategory.id;
                break;
            case 'company policy':
                companyPolicies[sopCategory.vesselId] = sopCategory.id;
                break;
            default:
                return protectFirestoreOperation(
                    `onCategoriesWrite delete vesselSopCategories ${sopCategory.id}`,
                    () => firestore.collection('vesselSopCategories').doc(sopCategory.id).delete()
                ).then(() => {
                    results.sopCategoriesDeleted++;
                    console.log(`[${results.sopCategoriesDeleted}] Deleted ${sopCategory.id} ${sopCategory.name}`);
                    return protectFirestoreOperation(
                        `onCategoriesWrite set whenVesselTouched ${sopCategory.vesselId}`,
                        () => firestore.collection('whenVesselTouched').doc(sopCategory.vesselId).set({
                            touched: FieldValue.serverTimestamp(),
                            vesselSopCategories: FieldValue.serverTimestamp(),
                        }, { merge: true })
                    );
                }).then(() => {
                    return processSopCategories();
                });
        }
        return processSopCategories();
    };
    const processVessels = (): Promise<void> => {
        if (vessels.length === 0) {
            return Promise.resolve();
        }
        const vessel = vessels.shift();
        const tasks = [];
        if (operationalProcedures[vessel.id] === undefined) {
            tasks.push({
                name: 'Operational Procedures',
                obj: operationalProcedures
            });
        }
        if (emergencies[vessel.id] === undefined) {
            tasks.push({
                name: 'Emergency',
                obj: emergencies
            });
        }
        if (companyPolicies[vessel.id] === undefined) {
            tasks.push({
                name: 'Company Policy',
                obj: companyPolicies
            });
        }
        const promises = [] as Promise<void>[];
        tasks.forEach((task) => {
            const newRef = firestore.collection('vesselSopCategories').doc();
            return newRef.set({
                vesselId: vessel.id,
                name: task.name,
                state: 'active'
            }).then(() => {
                results.sopCategoriesCreated++;
                task.obj[vessel.id] = newRef.id;
                return Promise.resolve();
            });
        });
        return Promise.all(promises).then(() => {
            return processVessels();
        });
    };
    const processSops = (): Promise<void> => {
        if (sops.length === 0) {
            return Promise.resolve();
        }
        const promises = [];
        for (let i = 0; i < 50; i++) {
            if (sops.length === 0) {
                break;
            }
            const sop = sops.shift();
            if (sop.vesselId && sop.type) {
                let name;
                let obj;
                switch(sop.type) {
                    case 'operationalProcedures':
                        name = 'Operational Procedures';
                        obj = operationalProcedures;
                        break;
                    case 'emergency':
                        name = 'Emergency';
                        obj = emergencies;
                        break;
                    case 'companyPolicy':
                        name = 'Company Policy';
                        obj = companyPolicies;
                        break;
                    default:
                        console.log(`>>> Weird type!`, sop);
                }
                if (obj && obj[sop.vesselId]) {
                    promises.push(
                        protectFirestoreOperation(
                            `onCategoriesWrite update SOPs ${sop.id}`,
                            () => firestore.collection('SOPs').doc(sop.id).set({
                                categoryId: obj[sop.vesselId],
                                type: FieldValue.delete(),
                                touched: FieldValue.serverTimestamp()
                            }, { merge: true })
                        ).then(() => {
                            results.sopsMigrated++;
                            if (results.sopsMigrated % 100 === 0) {
                                console.log(`${results.sopsMigrated} SOPs migrated.`);
                            }
                        })
                    );
                    promises.push(
                        firestore.collection('whenVesselTouched').doc(sop.vesselId).set({
                            touched: FieldValue.serverTimestamp(),
                            vesselSopCategories: FieldValue.serverTimestamp(),
                        }, { merge: true })
                    );
                } else {
                    console.log(`>>> Missing sop category!`, sop);
                }
            }
        }
        return Promise.all(promises).then(() => {
            return processSops();
        });
    };
    return protectFirestoreOperation(
        `onCategoriesWrite get vesselSopCategories`,
        () => firestore.collection('vesselSopCategories').get()
    ).then((snap: QuerySnapshot<DocumentData, DocumentData>) => {
        snap.docs.forEach((doc) => {
            sopCategories.push({
                id: doc.id,
                ...doc.data()
            });
        });
        return processSopCategories();
    }).then(() => {
        return protectFirestoreOperation(
            `onCategoriesWrite get vessels`,
            () => firestore.collection('vessels').get()
        );
    }).then((snap: QuerySnapshot<DocumentData, DocumentData>) => {
        snap.docs.forEach((doc) => {
            vessels.push({
                id: doc.id,
                ...doc.data()
            });
        });
        return processVessels();
    }).then(() => {
        console.log('>>> operationalProcedures', operationalProcedures);
        console.log('>>> emergencies', emergencies);
        console.log('>>> companyPolicies', companyPolicies);
        return protectFirestoreOperation(
            `onCategoriesWrite get SOPs`,
            () => firestore.collection('SOPs').get()
        );
    }).then((snap: QuerySnapshot<DocumentData, DocumentData>) => {
        snap.docs.forEach((doc) => {
            sops.push({
                id: doc.id,
                ...doc.data()
            });
        });
        return processSops();
    });
};

