import { SecretManagerServiceClient } from "@google-cloud/secret-manager";
import { settings } from "../projectSettings";
import { makeDateTime, setTimeZone } from "./datesAndTime";
import { firestore } from "../init";
import { createTransport } from "nodemailer";
import mg from "nodemailer-mailgun-transport";
import { processTasks } from "./util_firebase";


let mailgunApiKey: (string | undefined);
const getMailgunApiKey = (): Promise<string | undefined> => {
    if (mailgunApiKey) {
        return Promise.resolve(mailgunApiKey);
    }
    const client = new SecretManagerServiceClient();
    return client.accessSecretVersion({
        name: settings.mailgunApiKeySecretResourceName
    }).then((result) => {
        mailgunApiKey = result[0].payload!.data!.toString();
        return mailgunApiKey;
    });
};

const getMailgun = () => {
    return getMailgunApiKey().then((apiKey) => {
        const auth = {
            auth: {
                api_key: apiKey!,
                domain: settings.mailgunDomain,
            }
        };
        return createTransport(mg(auth));
    });
};

// Wrapper around the nodemailer sendMail function to allow us to have a simple method of sending
// emails with the information we want already loaded in the 'data' object.
//
// Data Object Form:
//
// data: {
//     from: <sender>,
//     to: <recipient>,
//     subject: <subject>,
//     html: <message>
//     [attachment]: <attached data>
// }
export const sendMail = (data: any): Promise<string> => {
    return getMailgun().then((mailgun) => {
        return mailgun.sendMail(data);
    }).then((msg: any) => {
        console.log(msg);
        return Promise.resolve('Email Successfully Sent.');
    }, (error: any) => {
        console.log(error);
        return Promise.reject('Error sending mail');
    });
}


export type NotificationReadyToSend = {
    id: string,
    to: string[],
    isDaily?: boolean
};

export const sendNotificationEmails = (
    notification: NotificationReadyToSend,
    subject: string,
    html: string
): Promise<any> => {
    if (settings.preventDirectEmails && settings.emailCopyTo === undefined) {
        console.log('Prevented sending notification due to settings');
        return Promise.resolve();
    }
    
    const emailTo = [] as string[];
    if (!settings.preventDirectEmails) {
        emailTo.push(...notification.to);
    }
    if (notification.isDaily) {
        if (settings.dailyNotificationCopyTo) {
            emailTo.push(...settings.dailyNotificationCopyTo);
        }
    } else if (settings.emailCopyTo) {
        emailTo.push(...settings.emailCopyTo);
    }
    console.log(`sendNotificationEmails ${subject} from ${settings.fromSeaFluxEmail} to`, emailTo);
    
    return processTasks(
        emailTo,
        (anEmail) => {
            return sendMail({
                from: settings.fromSeaFluxEmail,
                to: anEmail,
                subject: subject,
                html: html,
                attachments: [{
                    cid: 'logo.png',
                    content: logoBase64,
                    encoding: 'base64'
                }]
            })
        }
    ).then(() => {
        // Successfully sent email(s)
        return firestore.collection('notifications').doc(notification.id).set({
            whenSent: Date.now(),
            state: 'sent'
        }, { merge: true });
    }).catch((error: any) => {
        console.error(`Something went wrong sending notification ${notification.id}`, error);
        return firestore.collection('notifications').doc(notification.id).set({
            whenFailed: Date.now(),
            state: 'failed',
            error: error,
        }, { merge: true }).then(() => {
            return Promise.reject(error);
        });
    });
}


export const renderEmail = (
    heading?: string,
    link?: string,
    status: 'alert' | 'created' | 'updated' | 'completed' = 'created',
    tableRows?: string,
    timezone?: string,
    images?: any[],
    files?: any[]
) => {
    const width = 650;
    const pageColour = '#F5F6F8';
    return `
        ${renderTop(status, width, pageColour)}
        ${heading ? renderHeading(heading, status === 'alert', pageColour) : ''}
        ${tableRows ? renderTableRows(tableRows, pageColour) : ''}
        ${link ? renderViewOnSeaFluxLink(link, pageColour) : ''}
        ${images ? renderImages(images) : ''}
        ${files ? renderFiles(files) : ''}
        ${renderBottom(timezone)}
    `;
}

const renderTableRows = (tableRows: string, pageColour: string) => {
    return `<table class="data" style="background-color:${pageColour}">${tableRows}</table>`;
}
const renderHeading = (heading: string, isAlert: boolean, pageColour: string) => {
    return `
        <div style="font-size: 24px;Margin: 8px 0px 12px 0px;${isAlert ? 'color: #cf3c4f;' : ''};background-color:${pageColour}">${heading}</div>
        <div style="height: 1px;background-color: #DADBDF;Margin: 0px 0px 8px 0px;"></div>
    `;
}
const renderViewOnSeaFluxLink = (link: string, pageColour: string) => {
    return `
        <div style="height: 1px;background-color: #DADBDF;Margin: 8px 0px 0px 0px;"></div>
        <div style="padding: 12px 0px 20px 0px;text-align: left;background-color:${pageColour}"><a href="${link}">View on Sea Flux &gt;&gt;</a></div>
    `;
}
const renderImages = (images: any[]) => {
    let s = '';
    images.forEach((image) => {
        s += `<a href="${image.src}" target="_blank" rel="noopener noreferrer"><img src="${image.src}" alt="${image.name}" style="max-width: 618px" border="0"></a>`;
        s += '<div style="height: 8px;width: 100%;">&nbsp;</div>';
    });
    return s;
}
const renderFiles = (files: any[]) => {
    let s = '';
    if (files.length === 1) {
        s += 'See file: ';
    } else if (files.length > 1) {
        s += 'Associated files:<br>';
    }
    files.forEach((file) => {
        s += `<a href="${file.src}" download="${file.name}" target="_blank" rel="noopener noreferrer">${file.name}</a><br>`;
    });
    return s;
}

const getStatusColour = (status: 'alert' | 'created' | 'updated' | 'completed') => {
    switch (status) {
        case 'alert':
            return '#cf3c4f';
        case 'created':
            return '#373946';
        case 'updated':
            return '#373946';
        case 'completed':
            return '#6EB541';
        default:
            return '#373946';
    }
}

export const renderTop = (status: 'alert' | 'created' | 'updated' | 'completed', width = 650, pageColour = '#F5F6F8', moreStyle = '') => {
    return `
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://unpkg.com/ionicons@4.5.10-0/dist/css/ionicons.min.css" rel="stylesheet">
	<style type="text/css">
		body {
			Margin: 0;
			padding: 0;
			background-color: #7E808B;
		}
		table {
			border-spacing: 0;
		}
		td {
			padding: 0;
		}
		img {
			border: 0;
		}
		.wrapper {
			width: 100%;
			table-layout: fixed;
			background-color: #7E808B;
			padding: 0px 0px 40px 0px;
            Margin: 0px;
		}
		.webkit {
			max-width: ${width}px;
			background-color: ${pageColour};
            padding: 0px;
            Margin: 0px;
		}
		.outer {
			Margin: 0 auto;
			width: 100%;
			max-width: ${width}px;
			border-spacing: 0;
			font-family: 'Montserrat', sans-serif;
			color: #373946;
			font-size: 14px;
		}
		.data td {
			padding: 10px 0px 10px 20px;
		}
		.data td:first-of-type {
			padding-left: 0px;
		}
		.data .field {
			font-weight: bold;
            padding-left: 0px;
		}
        ${moreStyle}
		@media screen and (max-width: 650px) { 
		}
		@media screen and (max-width: 400px) { 
		}
	</style>
</head>
<body>
	<center class="wrapper">
		<div class="webkit">
			<table class="outer" align="center">
				<tr>
					<td>
						<table width="100%" style="border-spacing: 0;">
							<tr>
								<td style="background-color: ${getStatusColour(status)};padding: 18px 8px 12px 8px;text-align: center;">
									<a href="https://seaflux.netlify.app/"><img height=26 width=150 alt="Sea Flux" title="Logo" style="max-width: 100%; width: 150px; height: 26px;" src="cid:logo.png" /></a>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <table width="100%" style="border-spacing: 0;">
                                        <tr>
                                            <td class="content" style="padding: 8px ${moreStyle ? '0' : '16'}px 20px ${moreStyle ? '0' : '16'}px;background-color:${pageColour}">
`;
};
export const renderData = (field: string, value: any, sup?: string, dontSanitize?: boolean) => {
    return `<tr><td valign="top" class="field">${field}</td><td valign="top">${renderValue(value, dontSanitize)}${sup ? `<span style="font-size: 0.9em;position: relative;vertical-align: top;top: -0.4em;">${sup}</span>` : ''}</td></tr>`;
};
export const renderValue = (value: any, dontSanitize?: boolean) => {
    if (value !== undefined) {
        if ((typeof value === 'string' && value.startsWith('<a href=')) || dontSanitize) {
            return value; // Don't sanitize it
        }
        if (typeof value === 'string') {
            return value.replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/\n/g, '<br>');
        }
        return ''+value;
    }
    return '-';
};
export const renderBottom = (timezone?: string) => {
    let timezoneContent;
    if (timezone) {
        setTimeZone(timezone);
        const datetime = makeDateTime();
        timezoneContent = `
            <tr>
                <td style="padding: 20px 0px 0px 0px;text-align: center;font-size: 12px;">
                <span style="font-size: 0.9em;position: relative;vertical-align: top;top: -0.4em;">*</span>Timezone: ${datetime.zone.offsetName(Date.now(), {format: 'long'})} (${datetime.zone.offsetName(Date.now(), {format: 'short'})})
                </td>
            </tr>
        `;
    }
    return `
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <table width="100%" style="border-spacing: 0;background-color: #7E808B;color: #FFFFFF;">
                                        ${timezoneContent ? timezoneContent : ''}
                                        <tr>
                                            <td style="padding: 20px;text-align: center;font-size: 12px;">
                                                &copy; Copyright ${(new Date()).getFullYear()} Sea Flux Ltd.
                                                <p style="color: #CCCCCC; font-size: 11px;padding-top: 12px;">
                                                    This e-mail communication and any attachments may contain confidential and privileged information. If you are not the intended recipient, you are hereby notified that you have received this communication in error and that any review, disclosure, dissemination, distribution or copying of it or its contents is strictly prohibited.
                                                </p>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        </div>
    </center>
</body>
</html>
`;
}




export const logoBase64 = '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';

export const flagBase64 = '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';
