// For Firebase Functions documentation see: https://firebase.google.com/docs/functions/typescript
import { signIn } from "./users-and-security/signIn";
import { activateAccount } from "./users-and-security/activateAccount";
import { resetAccount } from "./users-and-security/resetAccount";
import { isEmailAvailable } from "./users-and-security/isEmailAvailable";
import { changeEmail } from "./users-and-security/changeEmail";
import { onUsersWrite } from "./users-and-security/onUsersWrite";
import { onUserPermissionsWrite } from "./users-and-security/onUserPermissionsWrite";
import { requestPasswordReset } from "./users-and-security/requestPasswordReset";
import { resetPassword } from "./users-and-security/resetPassword";
import { onWelcomeEmailsCreate } from "./users-and-security/onWelcomeEmailsCreate";
import { onDeviceInfoWrite } from "./users-and-security/onDeviceInfoWrite";
import { onLicenseeSettingsWrite } from "./users-and-security/onLicenseeSettingsWrite";
import { adminChangeLicensee, adminFixChangeLicenseeProblems, adminFixChangeLicenseeProblemsTrigger } from "./users-and-security/adminChangeLicensee";
import { onNotificationsCreate } from "./notifications/onNotificationsCreate";
import { scheduledDailyNotifications, adminDoDailyNotifications } from "./notifications/doDailyNotifications";
import { onSendWeeklyReportsCreate } from "./weekly-reports/onSendWeeklyReportsCreate";
import { scheduledWeeklyReports, adminDoWeeklyReports } from "./weekly-reports/doWeeklyReports";
import { onFilesUpdate } from "./files/onFilesUpdate";
import { onProcessImageFilesCreate, onRetryProcessImageFilesCreate } from "./files/processImageFiles";
import { onProcessSignatureFilesCreate, onRetryProcessSignatureFilesCreate } from "./files/processSignatureFiles";
import { onProcessRichTextFilesCreate, onRetryProcessRichTextFilesCreate } from "./files/processRichTextFiles";
import { scheduledProcessFilesToBeDeleted } from "./files/processFilesToBeDeleted";
import { adminAnalyseFiles, adminAnalyseFilesTrigger } from "./files/adminAnalyseFiles";
import { adminFixFileProblems } from "./files/adminFixFileProblems";
import { adminMaintainCategories, scheduledMaintainCategories } from "./categories/maintainCategories";
import { onCustomFormsUpdate } from "./custom-forms/onCustomFormsUpdate";
import { adminFixCustomFormIssues } from "./custom-forms/adminFixCustomFormIssues";
import { onCorrectiveActionsCreate } from "./general-firestore-triggers/onCorrectiveActionsCreate";
import { onSafetyCheckCompletedCreate } from "./general-firestore-triggers/onSafetyCheckCompletedCreate";
import { onMaintenanceTasksCompletedCreate } from "./general-firestore-triggers/onMaintenanceTasksCompletedCreate";
import { onJobsCreate } from "./general-firestore-triggers/onJobsCreate";
import { onIncidentsCreate } from "./general-firestore-triggers/onIncidentsCreate";
import { onEquipmentUpdate } from "./general-firestore-triggers/onEquipmentUpdate";
import { scheduledCustomFormsCleanup } from "./custom-forms/doCustomFormsCleanup";
import { refreshOverdueStats } from "./analysis/refreshOverdueStats";
import { onStorageFileSaved } from "./backup/onStorageFileSaved";
import { onStorageFileDeleted } from "./backup/onStorageFileDeleted";
import { onStorageFileMetadataUpdated } from "./backup/onStorageFileMetadataUpdated";
import { scheduledFirestoreBackup, adminDoFirestoreBackup } from "./backup/doFirestoreBackup";
import { adminFixBackupIssues, adminFixBackupIssuesTrigger } from "./backup/adminFixBackupIssues";
import { adminDoReplication } from "./section-replicator/adminDoReplication";
import { adminFixSectionReplicatorIssues } from "./section-replicator/adminFixReplicationIssues";
import { scheduledProcessTraceReports, adminProcessTraceReports } from "./error-reporting/processTraceReports";
import { scheduledProcessErrorReports, adminProcessErrorReports } from "./error-reporting/processErrorReports";
import { adminDoMigration } from "./migration/adminDoMigration";
import { adminDoMaintenance, adminDoMaintenanceTrigger } from "./maintenance/adminDoMaintenance";
import { adminDoCleanup, adminDoCleanupTrigger } from "./maintenance/adminDoCleanup";
import { adminDoDataSync } from "./maintenance/adminDoDataSync";
import { adminCalculateMetrics } from "./analysis/adminCalculateMetrics";
import { onCategoriesWrite } from "./categories/onCategoriesWrite";
import { refreshFleetStats } from "./analysis/stats/refreshFleetStats";

require("firebase-functions/logger/compat"); // Patches console.log, console.error etc. so that they use firebase logging instead.

export {
    // Tmp Fix!
    //adminFixAdani,
    //adminFixAdaniUndo,

    // Migration
    adminDoMigration,

    // Maintenance
    adminDoMaintenance,
    adminDoMaintenanceTrigger,
    adminDoCleanup,
    adminDoCleanupTrigger,
    adminDoDataSync,

    // Users and Security
    onUsersWrite,
    onUserPermissionsWrite,
    signIn,
    activateAccount,
    resetAccount,
    isEmailAvailable,
    changeEmail,
    requestPasswordReset,
    resetPassword,
    onWelcomeEmailsCreate,
    onDeviceInfoWrite,
    onLicenseeSettingsWrite,
    adminChangeLicensee,
    adminFixChangeLicenseeProblems,
    adminFixChangeLicenseeProblemsTrigger,

    // Analysis, Metrics, Stats etc.
    refreshOverdueStats,
    adminCalculateMetrics,
    refreshFleetStats,

    // Notifications
    onNotificationsCreate,
    scheduledDailyNotifications,
    adminDoDailyNotifications,

    // Weekly Reports
    onSendWeeklyReportsCreate,
    scheduledWeeklyReports,
    adminDoWeeklyReports,

    // Files / Images / Signatures / Rich Text Documents
    onFilesUpdate,
    onProcessImageFilesCreate,
    onProcessSignatureFilesCreate,
    onProcessRichTextFilesCreate,
    onRetryProcessImageFilesCreate,
    onRetryProcessSignatureFilesCreate,
    onRetryProcessRichTextFilesCreate,
    scheduledProcessFilesToBeDeleted,
    adminAnalyseFiles,
    adminAnalyseFilesTrigger,
    adminFixFileProblems,

    // Categories
    onCategoriesWrite, // Includes functions for all category collections
    adminMaintainCategories,
    scheduledMaintainCategories,

    // Custom Forms
    onCustomFormsUpdate,
    scheduledCustomFormsCleanup,
    adminFixCustomFormIssues,

    // General Firestore Triggers (miscellaneous Firestore functions that don't fit anywhere else)
    onCorrectiveActionsCreate,
    onSafetyCheckCompletedCreate,
    onMaintenanceTasksCompletedCreate,
    onJobsCreate,
    onIncidentsCreate,
    onEquipmentUpdate,

    // Backup Management (Firestore and Storage)
    scheduledFirestoreBackup,
    adminDoFirestoreBackup,
    onStorageFileSaved,
    onStorageFileDeleted,
    onStorageFileMetadataUpdated,
    adminFixBackupIssues,
    adminFixBackupIssuesTrigger,

    // Section Replicator
    adminDoReplication,
    adminFixSectionReplicatorIssues,

    // Error Reporting
    scheduledProcessTraceReports,
    adminProcessTraceReports,
    scheduledProcessErrorReports,
    adminProcessErrorReports,

}
