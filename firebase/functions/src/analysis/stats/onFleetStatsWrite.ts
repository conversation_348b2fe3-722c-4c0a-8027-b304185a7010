import { onDocumentWritten } from 'firebase-functions/firestore';
import { defaultEventHandlerOptions } from '../../eventHandlerOptions';
import {DocumentData, FieldValue, QuerySnapshot} from 'firebase-admin/firestore';
import { protectFirestoreOperation } from '../../lib/util_firebase';
import { firestore } from '../../init';
import { calculateStatsForSafetyCheckItems } from './safety/calculateSafetyCheckStats';
import { calculateStatsForSafetyEquipmentExpiry } from './safety/calculateSafetyEquipmentExpiryStats';
import { calculateStatsForDrills } from './safety/calculateDrillsStats';
import { calculateStatsForIncidents } from './healthSafety/calculateIncidentStats';
import { calculateStatsForDangerousGoods } from './healthSafety/calculateDangerousGoodsStats';
import { calculateStatsForRiskAssessment } from './healthSafety/calculateRiskAssessmentStats';
import {calculateStatsForMaintenanceSchedule} from "./maintenance/calculateMaintenanceScheduleStats";
import {calculateStatsForJobs} from "./maintenance/calculateJobStats";
import {calculateStatsForSpareParts} from "./maintenance/calculateSparePartsStats";
import {calculateStatsForVesselCertificates} from "./vesselRegister/calculateVesselCertificateStats";
import {calculateStatsForVesselDocuments} from "./vesselRegister/calculateVesselDocumentStats";
import {calculateStatsForCompanyDocuments} from "./companyRegister/calculateCompanyDocumentStats";
import {subtractIntervalMillis} from "../../lib/datesAndTime";
import {calculateStatsForCrewCertificates} from "./crew/calculateCrewCertificateStats";

export enum VesselCollectionsToRefresh {
    // Safety
    DRILLS = 'drills',
    SAFETY_CHECK_ITEMS = 'safetyCheckItems',
    SAFETY_EQUIPMENT_ITEMS = 'safetyEquipmentItems',

    // Maintenance
    SCHEDULED_MAINTENANCE_TASKS = 'scheduledMaintenanceTasks',
    JOBS = 'jobs',
    SPARE_PARTS = 'spareParts',

    // Vessel Document Register
    VESSEL_CERTIFICATES = 'vesselCertificates',
    VESSEL_DOCUMENTS = 'vesselDocuments',

    /** Licensee based collections */
    // Health & Safety
    INCIDENTS = 'incidents',
    RISK_ASSESSMENTS = 'risks',
    DANGEROUS_GOODS = 'dangerousGoods',
}

export enum CompanyCollectionsToRefresh {
    // Company Document Register
    COMPANY_DOCUMENTS = 'companyDocuments',

    // Crew
    CREW_CERTIFICATES = 'crewCertificates',
}

export type CollectionsToRefresh = VesselCollectionsToRefresh | CompanyCollectionsToRefresh;

export type CalculatedStats = {
    [key: string]: number;
}

export type CollectionStats = {
    whenUpdated: number;
    stale: boolean | undefined;
    stats: CalculatedStats
}

export type VesselStats = {
    whenUpdated: number;
    stale: boolean | undefined;
    collections: {
        [key in VesselCollectionsToRefresh]: CollectionStats
    };
}
export type CompanyStats = {
    whenUpdated: number;
    stale: boolean | undefined;
    collections: {
        [key in CompanyCollectionsToRefresh]: CollectionStats
    };
}

export type FleetStats = {
    whenUpdated: number;
    touched: FieldValue;
    stale: boolean | undefined;
    vessels: {
        [vesselId: string]: VesselStats;
    }
    company: CompanyStats;
}

// Eventually move to this
// export const onFleetStatsWrite = onDocumentWritten(
//     {
//         ...defaultEventHandlerOptions,
//         document: 'fleetStats/{id}'
//     },
//     (event) => {
//
//         const change = event.data!;
//         // console.log(`change.after?.data()`, change.after?.data());
//
//         const licenseeId = change.after.id;
//         const timeNow = Date.now();
//
//         if (!change.before.exists) { // Created fleetStats
//             // If new - Then get the list of vessel Ids and build the stats for all vessels in the Fleet
//             console.log(`CREATED - refresh stats for entire fleet`);
//             return recreateFleetStats(licenseeId, timeNow);
//         } else if (!change.after.exists) { // Deleted fleetStats
//             return Promise.resolve();
//         } else { // Updated fleetStats
//             return processAndUpdateFleetStats(change.after.data() as FleetStats, licenseeId, timeNow);
//         }
//     }
// )

export const processAndUpdateFleetStats = async (originalFleetStats: FleetStats, licenseeId: string, timeNow: number) => {

    const prior1Day = subtractIntervalMillis(timeNow, '1d');

    if (originalFleetStats) {
        // Case 1: Refresh the whole fleet
        if (originalFleetStats?.stale === true || originalFleetStats?.whenUpdated < prior1Day) {
            console.log(`UPDATED - refresh stats for entire fleet because fleet is stale`);
            await recreateFleetStats(licenseeId, timeNow);
            return Promise.resolve();
        } else {
            // TODO: decide if we need to refresh stats here for each vessel
            // Case 2: Refresh stats per vessel within the fleet
            const fleetStats: FleetStats = {
                whenUpdated: originalFleetStats?.whenUpdated ?? timeNow,
                touched: FieldValue.serverTimestamp(),
                stale: false,
                vessels: originalFleetStats?.vessels ?? {},
                company: originalFleetStats?.company ?? {},
            };
            let mustUpdateStats = false;

            // Handle Vessel stats being stale
            for (const [vesselId, originalVesselData] of Object.entries(fleetStats?.vessels)) {
                if (
                    originalVesselData?.stale === true ||
                    originalVesselData?.whenUpdated < prior1Day
                ) {
                    mustUpdateStats = true;
                    // Refresh stats for all collections for this vessel
                    console.log(`UPDATED - Vessel - refresh stats for all collections for vessel ${vesselId}`);
                    fleetStats.vessels[vesselId] = await getVesselStatsFor(vesselId, timeNow);
                } else {

                    for (const [collectionName, originalCollectionData] of Object.entries(originalVesselData?.collections)) {
                        if (
                            originalCollectionData?.stale === true ||
                            originalCollectionData?.whenUpdated < prior1Day
                        ) {
                            mustUpdateStats = true;
                            // Refresh stats per collection for this vessel
                            console.log(`UPDATED - Vessel - Collection - refresh stats for ${collectionName} for vessel ${vesselId}`);

                            const resultVesselStats = await getVesselStatsFor(vesselId, timeNow, [collectionName as VesselCollectionsToRefresh]);
                            if (resultVesselStats && resultVesselStats.collections) {
                                const existingCollections = fleetStats.vessels[vesselId]?.collections
                                fleetStats.vessels[vesselId] = {
                                    whenUpdated: originalVesselData?.whenUpdated ?? resultVesselStats.whenUpdated,
                                    stale: resultVesselStats.stale,
                                    collections: {
                                        ...existingCollections,
                                        ...resultVesselStats.collections
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // Handle Company stats being stale
            if (fleetStats.company?.stale === true) {
                mustUpdateStats = true;
                // Refresh stats for all collections for the company
                console.log(`UPDATED - Company - refresh stats for all collections of the company`);
                fleetStats.company = await refreshCompanyStats(licenseeId, timeNow);
            } else {

                const originalCompanyData = fleetStats.company;
                for (const [collectionName, originalCollectionData] of Object.entries(originalCompanyData?.collections)) {
                    if (
                        originalCollectionData?.stale === true ||
                        originalCollectionData?.whenUpdated < prior1Day
                    ) {
                        mustUpdateStats = true;
                        // Refresh stats per collection for the company
                        console.log(`UPDATED - Company - Collection - refresh stats for ${collectionName}`);

                        const resultCompanyStats = await refreshCompanyStats(licenseeId, timeNow, [collectionName as CompanyCollectionsToRefresh]);
                        if (resultCompanyStats && resultCompanyStats.collections) {
                            const existingCollections = fleetStats.company?.collections
                            fleetStats.company = {
                                whenUpdated: originalCompanyData?.whenUpdated ?? resultCompanyStats.whenUpdated,
                                stale: resultCompanyStats.stale,
                                collections: {
                                    ...existingCollections,
                                    ...resultCompanyStats.collections
                                }
                            }
                        }
                    }
                }
            }

            if (mustUpdateStats) {
                // Set the fleet stats
                await setFleetStats(licenseeId, fleetStats);
            }

            return Promise.resolve();
        }
    }
}

export const recreateFleetStats = async (licenseeId: string, timeNow: number) => {
    const vesselSnap: QuerySnapshot<DocumentData, DocumentData> = await protectFirestoreOperation(
        'get active vessels',
        () => firestore.collection('vessels')
            .where('licenseeId', '==', licenseeId)
            .where('state', '==', 'active')
            .get()
    );
    const vesselIds = vesselSnap.docs.map((doc) => doc.id);

    const fleetStats: FleetStats = {
        whenUpdated: timeNow,
        touched: FieldValue.serverTimestamp(),
        stale: false,
        vessels: {},
        // @ts-ignore
        company: {}
    };

    console.log(`Refresh stats for all vessels`, vesselIds);

    // Refresh stats for all vessels
    for (const vesselId of vesselIds) {
        fleetStats.vessels[vesselId] = await getVesselStatsFor(vesselId, timeNow);
    }
    fleetStats.company = await refreshCompanyStats(licenseeId, timeNow);
    // Set the fleet stats
    await setFleetStats(licenseeId, fleetStats);

    return Promise.resolve();
}

/** +++++++++++++++++++++++++++++++++++++++++ Vessel Stats - UTILS +++++++++++++++++++++++++++++++++++++++++ */

const getVesselStatsFor = async (vesselId: string, timeNow: number, collections?: VesselCollectionsToRefresh[]): Promise<VesselStats> => {
    if (!vesselId) {
        // TODO: Remove this type cast
        return {} as VesselStats;
    }

    // If no collections specified, then refresh all collections for the vessel
    if (!collections) {
        collections = Object.values(VesselCollectionsToRefresh);
    }

    const vesselStats: VesselStats = {
        whenUpdated: timeNow,
        stale: false,
        // @ts-ignore
        collections: {}
    };

    for (const collection of collections) {
        // Calculate stats for collection
        const collectionStats = await calculateVesselStatsForCollection(vesselId, collection, timeNow);

        vesselStats.collections[collection] = {
            whenUpdated: timeNow,
            stale: false,
            stats: collectionStats
        };
    }

    return vesselStats;
}

const calculateVesselStatsForCollection = (vesselId: string, collectionName: VesselCollectionsToRefresh, timeNow: number): Promise<CalculatedStats> => {
    switch (collectionName) {
        case VesselCollectionsToRefresh.SAFETY_CHECK_ITEMS:
            return calculateStatsForSafetyCheckItems(vesselId, collectionName);
        case VesselCollectionsToRefresh.SAFETY_EQUIPMENT_ITEMS:
            return calculateStatsForSafetyEquipmentExpiry(vesselId, collectionName);
        case VesselCollectionsToRefresh.DRILLS:
            return calculateStatsForDrills(vesselId, collectionName);
        case VesselCollectionsToRefresh.SCHEDULED_MAINTENANCE_TASKS:
            return calculateStatsForMaintenanceSchedule(vesselId, collectionName);
        case VesselCollectionsToRefresh.JOBS:
            return calculateStatsForJobs(vesselId, collectionName);
        case VesselCollectionsToRefresh.SPARE_PARTS:
            return calculateStatsForSpareParts(vesselId, collectionName);
        case VesselCollectionsToRefresh.VESSEL_CERTIFICATES:
            return calculateStatsForVesselCertificates(vesselId, collectionName);
        case VesselCollectionsToRefresh.VESSEL_DOCUMENTS:
            return calculateStatsForVesselDocuments(vesselId, collectionName);
        case VesselCollectionsToRefresh.INCIDENTS:
            return calculateStatsForIncidents(vesselId, collectionName);
        case VesselCollectionsToRefresh.RISK_ASSESSMENTS:
            return calculateStatsForRiskAssessment(vesselId, collectionName);
        case VesselCollectionsToRefresh.DANGEROUS_GOODS:
            return calculateStatsForDangerousGoods(vesselId, collectionName);
        default:
            return Promise.resolve({});
    }
}

/** +++++++++++++++++++++++++++++++++++++++++ Company Stats - UTILS +++++++++++++++++++++++++++++++++++++++++ */

const refreshCompanyStats = async (licenseeId: string, timeNow: number, collections?: CompanyCollectionsToRefresh[]): Promise<CompanyStats> => {
    if (!licenseeId) {
        // TODO: Remove this type cast
        return {} as CompanyStats;
    }

    // If no collections specified, then refresh all collections for the vessel
    if (!collections) {
        collections = Object.values(CompanyCollectionsToRefresh);
    }

    const companyStats: CompanyStats = {
        whenUpdated: timeNow,
        stale: false,
        // @ts-ignore
        collections: {}
    };

    for (const collection of collections) {
        // Calculate stats for collection
        const collectionStats = await calculateCompanyStatsForCollection(licenseeId, collection, timeNow);

        companyStats.collections[collection] = {
            whenUpdated: timeNow,
            stale: false,
            stats: collectionStats
        };
    }

    return companyStats;
}

const calculateCompanyStatsForCollection = (licenseeId: string, collectionName: CompanyCollectionsToRefresh, timeNow: number): Promise<CalculatedStats> => {
    switch (collectionName) {
        case CompanyCollectionsToRefresh.COMPANY_DOCUMENTS:
            return calculateStatsForCompanyDocuments(licenseeId, collectionName);
        case CompanyCollectionsToRefresh.CREW_CERTIFICATES:
            return calculateStatsForCrewCertificates(licenseeId, collectionName);
        default:
            return Promise.resolve({});
    }
}

/** +++++++++++++++++++++++++++++++++++++++++ UTILS +++++++++++++++++++++++++++++++++++++++++ */

export const getRecordsForCollection = async (collectionName: string, whereClauses: any[][]): Promise<QuerySnapshot<DocumentData, DocumentData>> => {
    let query: FirebaseFirestore.Query<DocumentData, DocumentData> = firestore.collection(collectionName);
    whereClauses.forEach((whereClause) => {
        query = query.where(whereClause[0], whereClause[1], whereClause[2]);
    });

    return await protectFirestoreOperation(
        `get ${collectionName}`,
        () => query.get()
    );
}

const setFleetStats = async (licenseeId: string, stats: FleetStats ) => {
    console.log('Calculated Stats -', {fleetStats: JSON.stringify(stats)});
    return await protectFirestoreOperation(
        'set fleetStats',
        () => firestore.collection('fleetStats').doc(licenseeId).set(stats, { merge: true })
    );
}
