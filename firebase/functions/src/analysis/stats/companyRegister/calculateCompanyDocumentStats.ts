import {CollectionsToRefresh, getRecordsForCollection} from "../onFleetStatsWrite";
import {getDayOffset, getToday, warnDays} from "../../../lib/datesAndTime";

export const calculateStatsForCompanyDocuments = async (licenseeId: string, collectionName: CollectionsToRefresh) => {
    const snap = await getRecordsForCollection(
        collectionName,
        [
            ['licenseeId', '==', licenseeId],
            ['state', '==', 'active']
        ]
    );
    const stats = {
        total: 0,
        expired: 0,
        upcoming60Days: 0,
        complete: 0,
    };

    if (snap.docs.length > 0) {
        stats['total'] = snap.docs.length;
        const today = getToday();
        const upcoming60Days = getDayOffset(warnDays.companyDocuments[0]);

        snap.docs.forEach((doc) => {
            const data = doc.data();
            if (data?.dateExpires) {
                if (data?.dateExpires < today) {
                    stats['expired']++;
                } else if (upcoming60Days && data?.dateExpires < upcoming60Days) {
                    stats['upcoming60Days']++;
                } else {
                    stats['complete']++;
                }
            }
        });
    }
    return stats;
}