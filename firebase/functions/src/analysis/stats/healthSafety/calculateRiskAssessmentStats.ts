import { CollectionsToRefresh, getRecordsForCollection } from '../onFleetStatsWrite';
import { getDayOffset, getToday, warnDays } from '../../../lib/datesAndTime';

export const calculateStatsForRiskAssessment = async (vesselId: string, collectionName: CollectionsToRefresh) => {
    const riskSnap = await getRecordsForCollection(
        collectionName,
        [
            ['vesselIds', 'array-contains', vesselId],
            ['state', '==', 'active']
        ]
    );

    const stats = {
        total: 0,
        overdue: 0,
        upcoming30Days: 0,
        complete: 0,
    };

    if (riskSnap.docs.length > 0) {
        stats['total'] = riskSnap.docs.length;
        const today = getToday();
        const upcoming30Days = getDayOffset(warnDays.riskRegister[0])

        riskSnap.docs.forEach((doc) => {
            const data = doc.data();
            if (data?.dateDue) {
                if (data?.dateDue && data?.dateDue < today) {
                    stats['overdue']++;
                } else if ((upcoming30Days && (data?.dateDue < upcoming30Days)) || data?.dateDue === today) {
                    stats['upcoming30Days']++;
                } else {
                    stats['complete']++;
                }
            }
        });
    }

    return stats;
}