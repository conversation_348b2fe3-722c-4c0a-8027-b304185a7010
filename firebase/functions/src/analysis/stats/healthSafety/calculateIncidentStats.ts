import { CollectionsToRefresh, getRecordsForCollection } from '../onFleetStatsWrite';

export const calculateStatsForIncidents = async (vesselId: string, collectionName: CollectionsToRefresh) => {
    const snap = await getRecordsForCollection(
        collectionName,
        [
            ['vesselId', '==', vesselId],
            // NOTE: Cannot filter by deleted
            // ['state', '!=', 'deleted']
        ]
    );
    const stats = {
        total: 0,
        draft: 0,
        forReview: 0,
        inReview: 0,
        complete: 0,
    };

    if (snap.docs.length > 0) {
        stats['total'] = snap.docs.length;
        snap.docs.forEach((doc) => {
            const data = doc.data();
            switch (data.state) {
                case 'draft':
                    stats['draft']++;
                    break;
                case 'forReview':
                    stats['forReview']++;
                    break;
                case 'inReview':
                    stats['inReview']++;
                    break;
                case 'completed':
                    stats['complete']++;
                    break;
                default:
                    break;
            }
        });
    }

    return stats;
}
