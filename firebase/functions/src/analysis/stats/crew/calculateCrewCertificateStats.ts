import {getDayOffset, getToday, warnDays} from "../../../lib/datesAndTime";
import {CollectionsToRefresh, getRecordsForCollection} from "../onFleetStatsWrite";

export const calculateStatsForCrewCertificates = async (licenseeId: string, collectionName: CollectionsToRefresh) => {
    const snap = await getRecordsForCollection(
        collectionName,
        [
            ['licenseeId', '==', licenseeId],
            ['state', '==', 'active']
        ]
    );
    const stats = {
        total: 0,
        expired: 0,
        upcoming60Days: 0,
        complete: 0,
    };

    if (snap.docs.length > 0) {
        stats['total'] = snap.docs.length;
        const today = getToday();
        const upcoming60Days = getDayOffset(warnDays.crewCertificates[0]);

        // TODO: More comprehensive stats for `MISSING` certificates that are compulsory
        snap.docs.forEach((doc) => {
            const data = doc.data();
            if (data?.dateExpires) {
                if (data?.dateExpires < today) {
                    stats['expired']++;
                } else if (upcoming60Days && data?.dateExpires < upcoming60Days) {
                    stats['upcoming60Days']++;
                } else {
                    stats['complete']++;
                }
            }
        });
    }
    return stats;
}