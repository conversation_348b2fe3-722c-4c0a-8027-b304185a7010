import {CollectionsToRefresh, getRecordsForCollection} from "../onFleetStatsWrite";
import {getDayOffset, getToday, warnDays} from "../../../lib/datesAndTime";

export const calculateStatsForMaintenanceSchedule = async (vesselId: string, collectionName: CollectionsToRefresh) => {
    const snap = await getRecordsForCollection(
        collectionName,
        [
            ['vesselId', '==', vesselId],
            ['state', '==', 'active'],
            ['dateDue', '!=', ''],
        ]
    );
    const stats = {
        total: 0,
        overdue: 0,
        upcoming: 0,
        upcoming7days: 0,
        // upcoming30days: 0,
        // upcoming60days: 0,
        // upcoming90days: 0,
    };

    if (snap.docs.length > 0) {
        stats['total'] = snap.docs.length;
        const today = getToday();
        // const warnDays = warnDays.maintenanceSchedule[0];
        const upcoming7Days = getDayOffset(warnDays.maintenanceSchedule[0]);
        const upcoming30Days = getDayOffset(warnDays.maintenanceSchedule[1]);
        const upcoming60Days = getDayOffset(warnDays.maintenanceSchedule[2]);
        const upcoming90Days = getDayOffset(warnDays.maintenanceSchedule[3]);

        snap.docs.forEach((doc) => {
            const data = doc.data();
            if (data?.dateDue) {
                if (data?.dateDue && data?.dateDue < today) {
                    stats['overdue']++;
                } else if (upcoming7Days && data?.dateDue <= upcoming7Days) {
                    stats['upcoming7days']++;
                // } else if (data?.dateDue <= upcoming30Days) {
                //     stats['upcoming30days']++;
                // } else if (data?.dateDue <= upcoming60Days) {
                //     stats['upcoming60days']++;
                // } else if (data?.dateDue <= upcoming90Days) {
                //     stats['upcoming90days']++;
                } else {
                    stats['upcoming']++;
                }
            }
        });
    }
    return stats;
}