import {CollectionsToRefresh, getRecordsForCollection} from "../onFleetStatsWrite";
import {getDayOffset, getToday, warnDays} from "../../../lib/datesAndTime";

export const calculateStatsForMaintenanceSchedule = async (vesselId: string, collectionName: CollectionsToRefresh) => {
    const snap = await getRecordsForCollection(
        collectionName,
        [
            ['vesselId', '==', vesselId],
            ['state', '==', 'active'],
            ['dateDue', '!=', ''],
        ]
    );
    const stats = {
        total: 0,
        overdue: 0,
        upcoming7days: 0,
        complete: 0,
    };

    if (snap.docs.length > 0) {
        stats['total'] = snap.docs.length;
        const today = getToday();
        const upcoming7Days = getDayOffset(warnDays.maintenanceSchedule[0]);

        snap.docs.forEach((doc) => {
            const data = doc.data();
            if (data?.dateDue) {
                if (data?.dateDue && data?.dateDue < today) {
                    stats['overdue']++;
                } else if (upcoming7Days && data?.dateDue <= upcoming7Days) {
                    stats['upcoming7days']++;
                } else {
                    stats['complete']++;
                }
            }
        });
    }
    return stats;
}