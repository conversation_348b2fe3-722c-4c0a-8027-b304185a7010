import { HttpsError, onCall } from 'firebase-functions/https';
import { defaultEventHandlerOptionsForHttps } from '../../eventHandlerOptions';
import {DocumentSnapshot} from "firebase-admin/firestore";
import {firestore} from "../../init";
import {protectFirestoreOperation} from "../../lib/util_firebase";
import {FleetStats, processAndUpdateFleetStats, recreateFleetStats} from "./onFleetStatsWrite";

export const refreshFleetStats = onCall({
    ...defaultEventHandlerOptionsForHttps,
    concurrency: 8,
    memory: '512MiB',
    timeoutSeconds: 5 * 60,
    // minInstances: 1 // Don't need a dedicated instance
}, (request, response) => {
    const data = request?.data;
    if (data === undefined || data?.licenseeId === undefined) {
        throw new HttpsError('failed-precondition', 'Missing request data');
    }

    const licenseeId: string = data.licenseeId;
    const timeNow = Date.now();

    return protectFirestoreOperation(
        'Latest fleet stats for licensee',
        () => firestore.collection('fleetStats')
            .doc(licenseeId)
            .get()
    ).then((fleetStatsSnap: DocumentSnapshot<FleetStats, FleetStats>) => {
        if (!fleetStatsSnap.exists) {
            console.log('Recreate Stats')
            return recreateFleetStats(licenseeId, timeNow)
                .then(() => {
                    console.log('Saved Stats');
                    return {result: 'success'};
                })
                .catch((error) => {
                    return {result: 'fail', error: error.message};
                });
        } else {
            const fleetStats = fleetStatsSnap.data();
            if (fleetStats) {
                console.log('Process existing Stats')
                return processAndUpdateFleetStats(fleetStats , licenseeId, timeNow)
                    .then(() => {
                        console.log('Saved Stats');
                        return {result: 'success'};
                    })
                    .catch((error) => {
                        return {result: 'fail', error: error.message};
                    });
            }
        }
        return {result: 'fail', error: 'Failed to update fleetStats'};
    });
})