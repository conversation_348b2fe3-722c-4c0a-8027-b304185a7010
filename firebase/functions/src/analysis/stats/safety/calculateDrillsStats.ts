import { CollectionsToRefresh, getRecordsForCollection } from '../onFleetStatsWrite';

enum DrillStats {
    total = 'total',
    missing = 'missing',
    overdue = 'overdue',
    upcoming7days = 'upcoming7days',
    upcoming = 'upcoming',
}

export const calculateStatsForDrills = async (vesselId: string, collectionName: CollectionsToRefresh) => {
    const drillsSnap = await getRecordsForCollection(
        collectionName,
        [
            ['vesselId', '==', vesselId],
            ['state', '==', 'active']
        ]
    );
    const drills = drillsSnap.docs.map((doc) => {
        return {
            id: doc.id,
            ...doc.data()
        };
    });

    const drillUsersSnap = await getRecordsForCollection(
        'users',
        [
            ['vesselIds', 'array-contains', vesselId],
            ['state', '==', 'active'],
            ['isStaff', '==', true],
            ['crewVesselIds', 'array-contains', vesselId]
        ]
    );
    const drillUsers = drillUsersSnap.docs.map((doc) => {
        return {
            id: doc.id,
            ...doc.data()
        };
    });

    const drillReportsSnap = await getRecordsForCollection(
        'drillReports',
        [
            ['vesselId', '==', vesselId],
            ['state', '==', 'active']
        ]
    );
    const drillReports = drillReportsSnap.docs.map((doc) => {
        return {
            id: doc.id,
            ...doc.data()
        };
    });

    const hydratedDrills = drills.map(drill => {
        const crew = drillUsers.reduce((result: any, user) => {
            if (!user.id) return result; // Skip if we don't have a user ID

            if (!!drill.crew && drill.crew[user.id]) {
                // If the user already exists under the new data system, use that
                return {
                    ...result,
                    [user.id]: drill.crew[user.id],
                };
            }
        })
        return { ...drill, crew };
    });

    const stats = {
        [DrillStats.total]: 0,
        [DrillStats.missing]: 0,
        [DrillStats.overdue]: 0,
        [DrillStats.upcoming7days]: 0,
        [DrillStats.upcoming]: 0,
    };

    const finalStats = drillUsers.reduce(
      (acc, user) => {
          const userDrillData = hydratedDrills.reduce(
            (childAcc, drill) => {
                const crewData =
                  drill.crew && user.id ? drill?.crew[user.id] : { dateDue: undefined, dateLastCompleted: undefined }

                const isDrillUnassigned = (!!user.id && drill?.notAssignedTo?.includes(user.id)) ?? false

                if (isDrillUnassigned) {
                    return childAcc
                }

                if (!crewData?.dateDue || !crewData.dateLastCompleted) {
                    return {
                        ...childAcc,
                        [SeaStatusType.Attention]: childAcc?.[SeaStatusType.Attention] + 1,
                    }
                }

                const updatedAcc = calculateStatusBatteryCount(crewData.dateDue, warnDays.drills[0], childAcc)
                return {
                    ...childAcc,
                    ...updatedAcc,
                }
            },
            {
                ...emptyStatusBattery,
            }
          )

          return {
              ...acc,
              [SeaStatusType.Critical]: acc?.[SeaStatusType.Critical] + userDrillData?.[SeaStatusType.Critical],
              [SeaStatusType.Error]: acc?.[SeaStatusType.Error] + userDrillData?.[SeaStatusType.Error],
              [SeaStatusType.Warning]: acc?.[SeaStatusType.Warning] + userDrillData?.[SeaStatusType.Warning],
              [SeaStatusType.Attention]: acc?.[SeaStatusType.Attention] + userDrillData?.[SeaStatusType.Attention],
              [SeaStatusType.Minor]: acc?.[SeaStatusType.Minor] + userDrillData?.[SeaStatusType.Minor],
              [SeaStatusType.Ok]: acc?.[SeaStatusType.Ok] + userDrillData?.[SeaStatusType.Ok],
          }
      },
      {
          ...emptyStatusBattery,
      }
    )

    // if (drillsSnap.docs.length > 0) {
    //     stats['total'] = drillsSnap.docs.length;
    //     const today = new Date(timeNow).toISOString();
    //
    //     drillsSnap.docs.forEach((doc) => {
    //         const data = doc.data();
    //         if (data?.hasFault) {
    //             stats['fault']++;
    //         }
    //         if (data?.dateDue && data?.dateDue < today) {
    //             stats['overdue']++;
    //         } else {
    //             stats['upcoming']++;
    //         }
    //     });
    // }

    return stats;
}