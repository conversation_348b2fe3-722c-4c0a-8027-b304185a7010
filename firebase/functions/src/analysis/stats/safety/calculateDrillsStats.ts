import { CollectionsToRefresh, getRecordsForCollection } from '../onFleetStatsWrite';
import {getDayOffset, getToday, warnDays} from "../../../lib/datesAndTime";

enum DrillStats {
    total = 'total',
    missing = 'missing',
    overdue = 'overdue',
    upcoming7days = 'upcoming7days',
    current = 'current',
}

export interface DrillData {
    addedBy:                string;
    crew?:                  DrillCrewData;
    dateDue?:               string;
    dateLastCompleted?:     string;
    deletedBy?:             string;
    interval:               string;
    name:                   string;
    notAssignedTo?:         string[];
    state:                  string;
    touched:                string;
    updatedBy?:             string;
    vesselId:               string;
    whenAdded:              number;
    whenDeleted?:           number;
    whenDue_old?:           number;
    whenLastCompleted_old?: number;
    whenUpdated?:           number;
}
export interface DrillCrewData {
    [crewId: string]: {
        dateDue: string
        dateLastCompleted: string
    }
}


export const calculateStatsForDrills = async (vesselId: string, collectionName: CollectionsToRefresh) => {
    const drillsSnap = await getRecordsForCollection(
        collectionName,
        [
            ['vesselId', '==', vesselId],
            ['state', '==', 'active']
        ]
    );
    const drills = drillsSnap.docs.map((doc) => {
        return {
            id: doc.id,
            ...doc.data()
        };
    });

    const drillUsersSnap = await getRecordsForCollection(
        'users',
        [
            ['crewVesselIds', 'array-contains', vesselId],
            ['state', '==', 'active'],
            ['isStaff', '==', true],
        ]
    );
    const drillUsers = drillUsersSnap.docs.map((doc) => {
        return {
            id: doc.id,
            ...doc.data()
        };
    });

    const hydratedDrills = drills.map((drill: any) => {
        const crew = drillUsers.reduce((result: any, user) => {
            if (!user.id) return result; // Skip if we don't have a user ID

            if (!!drill.crew && drill.crew[user.id]) {
                // If the user already exists under the new data system, use that
                return {
                    ...result,
                    [user.id]: drill.crew[user.id],
                };
            }
        })
        return { ...drill, crew };
    });

    const stats = {
        [DrillStats.total]: 0,
        [DrillStats.missing]: 0,
        [DrillStats.overdue]: 0,
        [DrillStats.upcoming7days]: 0,
        [DrillStats.current]: 0,
    };
    const today = getToday();
    const upcoming7Days = getDayOffset(warnDays.drills[0]);

    // Helper function to determine drill status for a user
    const getDrillStatus = (crewData: any): keyof typeof DrillStats => {
        if (!crewData?.dateDue || !crewData.dateLastCompleted) {
            return DrillStats.missing;
        }

        if (crewData.dateDue < today) {
            return DrillStats.overdue;
        } else if (upcoming7Days && crewData.dateDue < upcoming7Days) {
            return DrillStats.upcoming7days;
        } else {
            return DrillStats.current;
        }
    };

    // Calculate stats for each user-drill combination
    const finalStats = { ...stats };

    for (const user of drillUsers) {
        if (!user.id) continue;

        for (const drill of hydratedDrills) {
            if (!!user.id && drill?.notAssignedTo?.includes(user.id)) continue;

            const crewData = drill.crew?.[user.id] || { dateDue: undefined, dateLastCompleted: undefined };
            const status = getDrillStatus(crewData);

            finalStats[status]++;
        }
    }

    return finalStats;
}