import { DocumentData, FieldPath, FieldValue, QuerySnapshot } from "firebase-admin/firestore";
import { firestore } from "../init";
import { processTasks, protectFirestoreOperation, requireSuperAdmin } from "../lib/util_firebase";
import { defaultEventHandlerOptionsForHttps, maxTimeoutSeconds, defaultEventHandlerOptions, maxMemoryFor2Cpu } from "../eventHandlerOptions";
import { onCall } from "firebase-functions/https";
import { getStorage } from "firebase-admin/storage";
import { onDocumentCreated } from "firebase-functions/firestore";
import { formatTimeDuration, makeDateTime } from "../lib/datesAndTime";
import { idCharacters, isImage } from "../lib/util_common";
import { fileCollections, fileCollectionsArray } from "./filesConfig";


/**
 * Starts off the long process of gathering file information from multiple sources:
 * storage objects, the files collection, and all collections with references to files.
 * Once gathered together, inconsistencies can be found between the different sources of truth.
 * This also allows the "Files" tab under Super Admin to show the latest data, stats and problems.
 */
export const adminAnalyseFiles = onCall({
    ...defaultEventHandlerOptionsForHttps,
    memory: '256MiB'
}, (request, response) => {
    return requireSuperAdmin(request).then(() => {
        console.log('adminAnalyseFiles!');
        return protectFirestoreOperation(
            `Kick start _adminAnalyseFilesTrigger`,
            () => firestore.collection('_adminAnalyseFilesTrigger').add({
                whenStarted: Date.now(),
                doPhase: 0
                //doPhase: 3,
                //doPhase: 4
            })
        ).then(() => {
            return { message: 'success' };
        });
    });
});

/**
 * Augments adminAnalyseFiles so it can run over multiple sequential calls
 */
export const adminAnalyseFilesTrigger = onDocumentCreated(
    {
        ...defaultEventHandlerOptions,
        cpu: 2,
        memory: maxMemoryFor2Cpu,
        timeoutSeconds: maxTimeoutSeconds,
        document: '_adminAnalyseFilesTrigger/{id}'
    },
    (event) => {
        const data = {
            ...event.data!.data(),
            id: event.data!.id
        } as any;
        return Promise.resolve().then(() => {
            if (data.doPhase !== undefined) {
                const whenStarted = data.whenStarted as number;
                const results = {} as any;
                const subPhase: number = data.subPhase ? data.subPhase : 0;
                const runningCount: number = data.runningCount ? data.runningCount : 0;
                const index: number = data.index ? data.index : 0;
                const fieldIndex: number = data.fieldIndex ? data.fieldIndex : 0;
                const lastId: string = data.lastId ? data.lastId : '0';
                let totalSubPhases = 4;
                console.log(`(---) adminAnalyseFilesTrigger! (${formatTimeDuration(Date.now() - whenStarted)} taken so far)`);
                switch (data.doPhase) {
                    case 0:
                        return deleteFileInfo(results, runningCount).then(() => {
                            console.log('phase 0 RESULTS', results);
                            if (results.notCompleted) {
                                return protectFirestoreOperation(
                                    `adminAnalyseFilesTrigger doPhase=0, runningCount=${results.runningCount}`,
                                    () => firestore.collection('_adminAnalyseFilesTrigger').add({ doPhase: 0, runningCount: results.runningCount, whenStarted })
                                );
                            } else {
                                return protectFirestoreOperation(
                                    `adminAnalyseFilesTrigger trigger phase 1`,
                                    () => firestore.collection('_adminAnalyseFilesTrigger').add({ doPhase: 1, whenStarted })
                                );
                            }
                        });
                    case 1:
                        totalSubPhases = 10;
                        return gatherStorageFileInfo(results, subPhase, totalSubPhases).then(() => {
                            console.log(`phase 1-${subPhase} RESULTS`, results);
                            if (subPhase >= (totalSubPhases - 1)) { // On to the next phase
                                return protectFirestoreOperation(
                                    `adminAnalyseFilesTrigger trigger phase 2`,
                                    () => firestore.collection('_adminAnalyseFilesTrigger').add({ doPhase: 2, whenStarted })
                                );
                            } else { // On to the next subPhase
                                return protectFirestoreOperation(
                                    `adminAnalyseFilesTrigger doPhase=1, subPhase=${subPhase + 1}`,
                                    () => firestore.collection('_adminAnalyseFilesTrigger').add({ doPhase: 1, subPhase: (subPhase + 1), whenStarted })
                                );
                            }
                        });
                    case 2:
                        return gatherFilesFileInfo(results, index, runningCount).then(() => {
                            console.log('phase 2 RESULTS', results);
                            if (results.notCompleted) {
                                return protectFirestoreOperation(
                                    `adminAnalyseFilesTrigger doPhase=2 index=${results.index}`,
                                    () => firestore.collection('_adminAnalyseFilesTrigger').add({ doPhase: 2, index: results.index, runningCount: results.runningCount, whenStarted })
                                );
                            } else {
                                return protectFirestoreOperation(
                                    `adminAnalyseFilesTrigger trigger phase 3`,
                                    () => firestore.collection('_adminAnalyseFilesTrigger').add({
                                        doPhase: 3,
                                        whenStarted,
                                        index: 0,
                                        fieldIndex: 0,
                                        lastId: '0'
                                    })
                                );
                            }
                        });
                    case 3:
                        //totalSubPhases = 10;
                        results.index = data.index ? data.index : 0;
                        results.fieldIndex = data.fieldIndex ? data.fieldIndex :0;
                        results.lastId = data.lastId ? data.lastId : '0';
                        return gatherReferenceFileInfo(results).then(() => {
                            console.log(`phase 3 index=${index} fieldIndex=${fieldIndex} lastId=${lastId} RESULTS`, results);
                            if (results.notCompleted) {
                                return protectFirestoreOperation(
                                    `adminAnalyseFilesTrigger doPhase=3 index=${results.index} fieldIndex=${results.fieldIndex}`,
                                    () => firestore.collection('_adminAnalyseFilesTrigger').add({
                                        doPhase: 3,
                                        index: results.index,
                                        fieldIndex: results.fieldIndex,
                                        lastId: results.lastId,
                                        whenStarted
                                    })
                                );
                            } else { // On to the next phase
                                return protectFirestoreOperation(
                                    `adminAnalyseFilesTrigger trigger phase 4`,
                                    () => firestore.collection('_adminAnalyseFilesTrigger').add({
                                        doPhase: 4,
                                        whenStarted
                                    })
                                );
                            }
                        });
                    case 4:
                        return compileFileInfoData(index, runningCount, results).then(() => {
                            console.log('phase 4 RESULTS', results);
                            if (results.notCompleted) {
                                return protectFirestoreOperation(
                                    `adminAnalyseFilesTrigger doPhase=4 index=${results.index}`,
                                    () => firestore.collection('_adminAnalyseFilesTrigger').add({
                                        doPhase: 4,
                                        index: results.index,
                                        runningCount: results.runningCount,
                                        whenStarted
                                    })
                                );
                            } else {
                                return protectFirestoreOperation(
                                    `adminAnalyseFilesTrigger trigger phase 5`,
                                    () => firestore.collection('_adminAnalyseFilesTrigger').add({ doPhase: 5, whenStarted })
                                );
                            }
                        });
                    default:
                        console.log(`RESULTS data. (took ${formatTimeDuration(Date.now() - whenStarted)})`, results);
                        return Promise.resolve();
                }
            }
            return Promise.resolve();
        }).then(() => {
            return protectFirestoreOperation(
                `adminAnalyseFilesTrigger delete _adminAnalyseFilesTrigger snap.id=${data.id}`,
                () => firestore.collection('_adminAnalyseFilesTrigger').doc(data.id).delete()
            );
        }).then(() => {
            console.log('Deleted _adminAnalyseFilesTrigger id='+data.id);
        });
    }
);




const deleteFileInfo = (results: any, runningCount: number) => {
    console.log(`(---) deleteFileInfo! runningCount=${runningCount}`);
    const documentsLimit = 50000;
    results.countFileInfoDelete = runningCount;
    const fileInfoIds = [] as string[];
    let count = 0;
    const processFileInfoIds = (): Promise<void> => {
        if (fileInfoIds.length === 0) {
            return Promise.resolve();
        }
        const promises = [];
        for (let i = 0; i < 20; i++) {
            if (fileInfoIds.length === 0) {
                break;
            }
            const fileInfoId = fileInfoIds.shift()!;
            count++;
            if (count % 1000 === 0) {
                console.log(`Deleted ${count} _fileInfo documents`);
            }
            promises.push(
                protectFirestoreOperation(
                    `Delete _fileInfo ${fileInfoId}`,
                    () => firestore.collection('_fileInfo').doc(fileInfoId).delete()
                ).then(() => {
                    results.countFileInfoDelete++;
                })
            );
        }
        return Promise.all(promises).then(() => {
            if (count >= documentsLimit) { // Abandon function to continue later
                results.notCompleted = true;
                results.runningCount = results.countFileInfoDelete;
                return Promise.resolve();
            }
            return processFileInfoIds();
        });
    };

    return protectFirestoreOperation(
        `get _fileInfo to delete`,
        () => firestore.collection('_fileInfo').limit(documentsLimit).get()
    ).then((snap: QuerySnapshot<DocumentData, DocumentData>) => {
        console.log(`deleteFileInfo! snap.docs.length=${snap.docs.length}`);
        snap.docs.forEach((doc) => {
            fileInfoIds.push(doc.id);
        });
        return processFileInfoIds();
    }).catch((error) => {
        return Promise.reject(error);
    });
};


const gatherStorageFileInfo = (results: any, subPhase: number, totalSubPhases: number) => {
    console.log(`(---) gatherStorageFileInfo! subPhase ${subPhase+1}/${totalSubPhases}`);
    results.countUniqueIds = 0;
    results.countFiles = 0;
    results.countOriginal = 0;
    results.countFull = 0;
    results.countCompressed = 0;
    const bucket = getStorage().bucket();
    const filesMap = {} as any;
    const filesArray = [] as any[];
    let count = 0;
    const groupSize = Math.ceil(idCharacters.length / totalSubPhases);
    let matchCharacters = idCharacters.substring(
        groupSize * subPhase,
        groupSize * (subPhase + 1)
    );
    console.log(`Getting files starting with ${matchCharacters}`);

    const processFiles = (): Promise<void> => {
        if (filesArray.length === 0) {
            return Promise.resolve();
        }

        const promises = [];
        for (let i = 0; i < 10; i++) {
            if (filesArray.length === 0) {
                break;
            }
            const file = filesArray.shift();
            count++;
            if (count % 1000 === 0) {
                console.log(`    Saved ${count} storage files to _fileInfo`);
            }
            const docId = file.id;
            delete file.id;

            // determine state
            if (
                (file.sizes.F && file.sizes.M && file.sizes.T) ||
                file.sizes.S ||
                file.sizes.R
            ) {
                file.state = 2;
            } else {
                file.state = 1;
            }

            promises.push(
                protectFirestoreOperation(
                    `Update _fileInfo with Storage data ${docId}`,
                    () => firestore.collection('_fileInfo').doc(docId).set({
                        storage: file,
                        touched: FieldValue.serverTimestamp()
                    }, { merge: true })
                )
            );
        }
        return Promise.all(promises).then(() => {
            //if (count >= 10000) return Promise.resolve();
            return processFiles();
        });
    };

    const processMatchCharacters = (): Promise<void> => {
        if (matchCharacters.length === 0) {
            return Promise.resolve();
        }
        const matchCharacter = matchCharacters[0];
        matchCharacters = matchCharacters.substring(1, matchCharacters.length);
        return bucket.getFiles({
            prefix: `files/${matchCharacter}`
        }).then(([files]) => {
            console.log(`files/${matchCharacter}* files.length:`, files.length);
            //console.log('>>> files[0].length:', files.length);
            files.forEach((_file) => {
                results.countFiles++;
                if (results.countFiles === 1) {
                    console.log('>>> Example _file', _file);
                }
                const components = _file.name.split('/');
                if (components.length !== 2) {
                    console.log(`UNEXPECTED path ${_file.name}`);
                    return;
                }
                //const licenseeId = components[1];
                const fileId = components[1].substring(0, 20);
                const variation = components[1].substring(20);
                let licenseeIds;
                if (_file.metadata && _file.metadata.metadata && _file.metadata.metadata.licenseeIds) {
                    licenseeIds = _file.metadata.metadata.licenseeIds;
                }
                //console.log(`licenseeId=${licenseeId} id=${fileId} variation=${variation}`);
    
                if (filesMap[fileId] === undefined) {
                    results.countUniqueIds++;
                    filesMap[fileId] = {
                        id: fileId,
                        licenseeIds: licenseeIds,
                        variations: [],
                        metadata: [],
                        numCompressed: 0,
                        //size: _file.metadata.size,
                        sizes: {},
                        contentType: _file.metadata.contentType,
                        timeCreated: _file.metadata.timeCreated,
                        ext: variation.substring(variation.lastIndexOf('.')),
                    };
                    filesArray.push(filesMap[fileId]);
                }

                const file = filesMap[fileId];

                let fileType = 'O'; // Original file
                if (variation.startsWith('_full')) {
                    fileType = 'F'; // Full image
                } else if (variation.startsWith('_medium')) {
                    fileType = 'M'; // Medium image
                } else if (variation.startsWith('_tiny')) {
                    fileType = 'T'; // Thumbnail image
                } else if (variation.startsWith('_sig')) {
                    fileType = 'S'; // Optimised signature png
                } else if (variation.startsWith('_opt')) {
                    fileType = 'R'; // Optimised rich text document
                }
                file.sizes[fileType] = parseInt(_file.metadata.size as string);

                file.variations.push(variation);
                file.metadata.push({
                    variation: variation,
                    ..._file.metadata.metadata
                });
                if (variation[0] === '.') {
                    results.countOriginal++;
                    file.hasOriginal = true;
                } else if (variation.startsWith('_full')) {
                    results.countFull++;
                    file.hasFull = true;
                } else {
                    results.countCompressed++;
                    file.hasCompressed = true;
                    file.numCompressed++;
                }
    
            });
            return processMatchCharacters();
        });
    };

    return processMatchCharacters().then(() => {
        return processFiles();
    }).then(() => {
        return Promise.resolve();
    });
};


const gatherFilesFileInfo = (results: any, index: number, runningCount: number) => {
    console.log(`(---) gatherFilesFileInfo! runningCount=${runningCount} index=${index}`);
    const documentsLimit = 40000;
    results.countFileDocs = runningCount;
    let count = 0;
    const files = [] as any[];

    const processIdCharacters = (): Promise<void> => {
        if (count >= documentsLimit || index >= idCharacters.length) {
            return Promise.resolve();
        }
        let query = firestore.collection('files')
        .where(FieldPath.documentId(), '>=', idCharacters[index])
        if (index + 1 < idCharacters.length) {
            query = query.where(FieldPath.documentId(), '<', idCharacters[index+1]);
        }

        return protectFirestoreOperation(
            `gatherFilesFileInfo get files`,
            () => query.get()
        ).then((snap: QuerySnapshot<DocumentData, DocumentData>) => {
            console.log(`Got files with ids ${idCharacters[index]}-${(index + 1 < idCharacters.length) ? idCharacters[index + 1] : 'end'} length=${snap.docs.length}`);
            snap.docs.forEach((doc) => {
                count++;
                files.push({
                    id: doc.id,
                    ...doc.data()
                });
            });
            index++;
            return processIdCharacters();
        });
    };

    let countFiles = 0;
    const processFiles = (): Promise<void> => {
        if (files.length === 0) {
            return Promise.resolve();
        }
        const promises = [];
        for (let i = 0; i < 10; i++) {
            if (files.length === 0) {
                break;
            }
            const file = files.shift();
            countFiles++;
            if (countFiles % 1000 === 0) {
                console.log(`    Saved ${countFiles} files to _fileInfo`);
            }
            const docId = file.id;
            delete file.id;
            promises.push(
                protectFirestoreOperation(
                    `Update _fileInfo with files data ${docId}`,
                    () => firestore.collection('_fileInfo').doc(docId).set({
                        file: file,
                        touched: FieldValue.serverTimestamp()
                    }, { merge: true} )
                )
            );
        }
        return Promise.all(promises).then(() => {
            return processFiles();
        });
    };

    return processIdCharacters().then(() => {
        return processFiles();
    }).then(() => {
        results.index = index;
        results.runningCount = runningCount + count;
        results.countFileDocs = results.runningCount;
        if (index < idCharacters.length) {
            results.notCompleted = true;
        }
        return Promise.resolve();
    });
};



const gatherReferenceFileInfo = (results: any) => {
    const fileCollectionKeys = [...fileCollectionsArray];
    console.log(`(---) gatherReferenceFileInfo! index=${results.index}/${fileCollectionKeys.length} fieldIndex=${results.fieldIndex} lastId=${results.lastId}`);
    
    const maxItemsToProcess = 50000;
    const itemsPerGet = 10000;
    const itemsPerCustomFormsGet = 1000;
    let itemsProcessed = 0;


    results.countCollections = 0;
    results.countCustomFormFilesElements = 0;
    results.countCustomFormSignatureElements = 0;
    results.countCustomFormHelpFiles = 0;

    const extractState = (fileValue?: string) => {
        if (fileValue && typeof fileValue === 'string' && !fileValue.startsWith('undefined')) {
            return parseInt(fileValue.substring(0,1));
        }
        return undefined;
    };
    const extractId = (fileValue?: string) => {
        if (fileValue && typeof fileValue === 'string') {
            if (fileValue.startsWith('undefined')) {
                return fileValue.substring(9,29);
            }
            return fileValue.substring(1,21);
        }
        return undefined;
    };

    const processTask = (task: any): Promise<void> => {
        const o = {
            collection: task.collection,
            field: task.field,
            state: task.state,
            ext: task.ext,
            docId: task.docId,
            when: task.when
        } as any;
        if (task.version) {
            o.version = task.version;
        }
        if (task.type) {
            o.type = task.type;
        }
        return protectFirestoreOperation(
            `Update _fileInfo with reference data ${task.fileId}`,
            () => firestore.collection('_fileInfo').doc(task.fileId).set({
                refs: FieldValue.arrayUnion(o),
                touched: FieldValue.serverTimestamp()
            }, { merge: true })
        );
    };

    let count = 0;
    const processTasks = (tasks: any[]): Promise<void> => {
        if (tasks.length === 0) {
            return Promise.resolve();
        }
        const promises = [];
        for (let i = 0; i < 20; i++) {
            if (tasks.length === 0) {
                break;
            }
            count++;
            if (count % 1000 === 0) {
                console.log(`    Saved ${count} references to _fileInfo`);
            }
            promises.push(processTask(tasks.shift()));
        }
        return Promise.all(promises).then(() => {
            return processTasks(tasks);
        });
    };

    const processCustomFormElements = (elements: any[]): Promise<void> => {
        if (elements.length === 0) {
            return Promise.resolve();
        }
        const element = elements.shift();

        if (element.type === 'signature') {
            results.countCustomFormSignatureElements++;
        } else if (element.type === 'files') {
            results.countCustomFormFilesElements++;
        }

        return protectFirestoreOperation(
            `processCustomFormElements get customFormsCompleted customFormId=${element.customFormId} version=${element.version}`,
            () => firestore.collection('customFormsCompleted')
            .where('customFormId', '==', element.customFormId)
            .where('version', '==', element.version)
            .get()
        ).then((snap: QuerySnapshot<DocumentData, DocumentData>) => {
            const tasks = [] as any[];
            snap.docs.forEach((doc) => {
                const completedForm = {
                    id: doc.id,
                    ...doc.data()
                } as any;
                const value = completedForm.data[`e${element.n}`];
                if (value && value.length > 0) {
                    if (element.type === 'signature') {
                        tasks.push({
                            collection: 'customFormsCompleted',
                            field: `e${element.n}`,
                            docId: completedForm.id,
                            version: element.version,
                            type: element.type,
                            state: extractState(value),
                            ext: value.substring(value.lastIndexOf('.')+1),
                            fileId: extractId(value),
                            when: completedForm.whenUpdated || completedForm.whenAdded
                        });
                    } else if (element.type === 'files') {
                        value.forEach((file: string) => {
                            tasks.push({
                                collection: 'customFormsCompleted',
                                field: `e${element.n}`,
                                docId: completedForm.id,
                                version: element.version,
                                type: element.type,
                                state: extractState(file),
                                ext: file.substring(file.lastIndexOf('.')+1),
                                fileId: extractId(file),
                                when: completedForm.whenUpdated || completedForm.whenAdded
                            });
                        });
                    }
                }
            });
            itemsProcessed += tasks.length;
            return processTasks(tasks).then(() => {
                return processCustomFormElements(elements);
            });
        });
    };

    const customFormVersions = [] as any[];
    const processCustomFormVersions = (): Promise<void> => {
        if (customFormVersions.length === 0) {
            return Promise.resolve();
        }
        const version = customFormVersions.shift();

        const elements = [] as any[];
        const tasks = [] as any[];
        if (version.form) {
            Object.keys(version.form).forEach((key) => {
                const element = version.form[key];
                if (element.id === 'signature' || element.id === 'files') {
                    elements.push({
                        type: element.id,
                        customFormId: version.customFormId,
                        version: version.version,
                        n: element.n
                    });
                }
                if (element.help && element.help.files) {
                    results.countCustomFormHelpFiles++;
                    element.help.files.forEach((file: string) => {
                        tasks.push({
                            collection: 'customFormVersions',
                            field: `e${element.n}`,
                            docId: version.id,
                            version: version.version,
                            state: extractState(file),
                            ext: file.substring(file.lastIndexOf('.')+1),
                            fileId: extractId(file),
                            when: version.whenUpdated || version.whenAdded
                        });
                    });
                }
            });
        }

        return processCustomFormElements(elements).then(() => {
            itemsProcessed += tasks.length;
            return processTasks(tasks);
        }).then(() => {
            return processCustomFormVersions();
        });
    };

    const processCollectionFields = (): Promise<void> => {
        if (itemsProcessed < maxItemsToProcess) {
            const collection = fileCollectionKeys[results.index];
            const fields = [...(fileCollections as any)[collection]];
            const field = fields[results.fieldIndex];
            console.log(`<> ${collection}.${field} lastId=${results.lastId}`);
            let isDone = true;
            return Promise.resolve().then(() => {
                if (field === 'form...') {
                    // Will get handled by data...
                } else if (field === 'data...') {
                    return protectFirestoreOperation(
                        `processCollectionFields get customFormVersions`,
                        () => firestore.collection('customFormVersions')
                        .where(FieldPath.documentId(), '>', results.lastId)
                        .limit(itemsPerCustomFormsGet)
                        .get()
                    ).then((snap: QuerySnapshot<DocumentData, DocumentData>) => {
                        console.log(`   customFormVersions length=${snap.docs.length}; first id=${snap.docs[0]?.id}`);
                        isDone = (snap.docs.length < itemsPerCustomFormsGet);
                        snap.docs.forEach((doc) => {
                            results.lastId = doc.id;
                            itemsProcessed++;
                            customFormVersions.push({
                                id: doc.id,
                                ...doc.data()
                            });
                        });
                        return processCustomFormVersions();
                    });
                } else {
                    return protectFirestoreOperation(
                        `processCollectionFields get non null for collection=${collection} field=${field}`,
                        () => firestore.collection(collection)
                        .where(FieldPath.documentId(), '>', results.lastId)
                        .where(field, '!=', null)
                        .limit(itemsPerGet)
                        .get()
                    ).then((snap: QuerySnapshot<DocumentData, DocumentData>) => {
                        console.log(`   length=${snap.docs.length}; first id=${snap.docs[0]?.id}`);
                        isDone = (snap.docs.length < itemsPerGet);
                        const tasks = [] as any[];
                        snap.docs.forEach((doc) => {
                            results.lastId = doc.id;
                            itemsProcessed++;
                            if (doc.data()[field]) {
                                const document = doc.data();
                                if (field === 'sfdoc') {
                                    Object.keys(document[field]).forEach((key) => {
                                        const value = document[field][key];
                                        //console.log(`>>> sfdoc ${doc.id} value=${value}`);
                                        tasks.push({
                                            collection: collection,
                                            field: field,
                                            docId: doc.id,
                                            state: extractState(value),
                                            ext: 'sfdoc',
                                            fileId: extractId(value),
                                            when: document.whenCompleted || document.whenUpdated || document.whenAdded
                                        });
                                    });
                                } else if (typeof document[field] === 'string') {
                                    tasks.push({
                                        collection: collection,
                                        field: field,
                                        docId: doc.id,
                                        state: extractState(document[field]),
                                        ext: document[field].substring(document[field].lastIndexOf('.')+1),
                                        fileId: extractId(document[field]),
                                        when: document.whenCompleted || document.whenUpdated || document.whenAdded
                                    });
                                } else if (document[field].length > 0) {
                                    document[field].forEach((fileId: string) => {
                                        if (fileId && typeof fileId === 'string') {
                                            tasks.push({
                                                collection: collection,
                                                field: field,
                                                docId: doc.id,
                                                state: extractState(fileId),
                                                ext: fileId.substring(fileId.lastIndexOf('.')+1),
                                                fileId: extractId(fileId),
                                                when: document.whenCompleted || document.whenUpdated || document.whenAdded
                                            });
                                        } else {
                                            console.log(`! Bad fileId for doc.id=${doc.id}`, document[field]);
                                        }
                                    });
                                }
                            }
                        });

                        return processTasks(tasks).then(() => {
                            return Promise.resolve();
                        });
                    });
                }
                return Promise.resolve();
            }).then(() => {
                if (!isDone) {
                    // Next lastId
                    return processCollectionFields();
                } else if (results.fieldIndex < fields.length - 1) {
                    // Next field
                    results.fieldIndex++;
                    results.lastId = '0';
                    return processCollectionFields();
                } else if (results.index < fileCollectionKeys.length - 1) {
                    // Next collection
                    results.index++;
                    results.fieldIndex = 0;
                    results.lastId = '0';
                    return processCollectionFields();
                }
                return Promise.resolve();
            });
        } else {
            results.notCompleted = true;
            return Promise.resolve();
        }
    }

    return processCollectionFields();
};


// Compile _fileInfoData using _fileInfo
const compileFileInfoData = (index: number, runningCount: number, results: any) => {
    console.log(`(---) compileFileInfoData! index=${index} runningCount=${runningCount}`);
    const documentsLimit = 100000;
    const itemsPerFileInfoData = 100;
    const fileInfo = [] as any[];
    const fileInfoData = [] as any[];
    let count = 0;
    return Promise.resolve().then(() => {
        if (index === 0) { // First phase, therefore delete any existing data
            let ids;
            return protectFirestoreOperation(
                `compileFileInfoData get _fileInfoData`,
                () => firestore.collection('_fileInfoData').get()
            ).then((snap: QuerySnapshot<DocumentData, DocumentData>) => {
                ids = snap.docs.map((doc) => {
                    return doc.id;
                });
                console.log(`Deleting ${ids.length} existing _fileInfoData...`);
                return processTasks(
                    ids,
                    (id) => {
                        return protectFirestoreOperation(
                            `Delete _fileInfoData ${id}`,
                            () => firestore.collection('_fileInfoData').doc(id).delete()
                        );
                    }, {
                        batchSize: 100,
                        logProgressEvery: 100,
                        itemsName: 'delete _fileInfoData tasks'
                    }
                );
            }).then(() => {
                console.log(`Successfully deleted ${ids.length} _fileInfoData documents.`);
            });
        }
        return Promise.resolve();
    }).then(() => {
        const processIdCharacters = (): Promise<void> => {
            if (count >= documentsLimit || index >= idCharacters.length) {
                return Promise.resolve();
            }
            let query = firestore.collection('_fileInfo')
            .where(FieldPath.documentId(), '>=', idCharacters[index])
            if (index + 1 < idCharacters.length) {
                query = query.where(FieldPath.documentId(), '<', idCharacters[index+1]);
            }
    
            return protectFirestoreOperation(
                `compileFileInfoData get _fileInfo`,
                () => query.get()
            ).then((snap: QuerySnapshot<DocumentData, DocumentData>) => {
                console.log(`Got _fileInfo with ids like ${idCharacters[index]}* length=${snap.docs.length}`);
                snap.docs.forEach((doc) => {
                    count++;
                    fileInfo.push({
                        id: doc.id,
                        ...doc.data()
                    });
                });
                index++;
                return processIdCharacters();
            });
        };

        return processIdCharacters().then(() => {

            let itemCount = 0;
            let currentFileInfoData = {
                items: []
            } as any;
            fileInfoData.push(currentFileInfoData);

            fileInfo.forEach((info) => {
                if (itemCount >= itemsPerFileInfoData) {
                    itemCount = 0;
                    currentFileInfoData = {
                        items: []
                    };
                    fileInfoData.push(currentFileInfoData);
                }

                if (info.touched) {
                    currentFileInfoData.touched = info.touched;
                }

                const o = {
                    id: info.id,
                    licenseeIds: [],
                    problems: {}
                } as any;

                // when
                if (info.file && info.file.whenAdded) {
                    o.when = info.file.whenAdded;
                } else if (info.storage && info.storage.metadata && info.storage.metadata[0] && info.storage.metadata[0].whenUploaded) {
                    o.when = parseInt(info.storage.metadata[0].whenUploaded);
                } else if (info.storage && info.storage.timeCreated) {
                    o.when = makeDateTime(info.storage.timeCreated).toMillis();
                } else if (info.refs && info.refs.length > 0) {
                    info.refs.reverse().forEach((ref: any) => {
                        if (ref.when) {
                            o.when = ref.when;
                        }
                    });
                }

                // collection
                if (info.refs && info.refs[0] && info.refs[0].collection) {
                    o.collection = info.refs[0].collection;
                } else if (info.file && info.file.refs && info.file.refs[0] && info.file.refs[0].collection) {
                    o.collection = info.file.refs[0].collection;
                }

                // isSignature
                if (info.file && info.file.isSignature) {
                    o.isSignature = true;
                }

                // ext
                if (info.file && info.file.ext) {
                    o.ext = info.file.ext;
                } else if (info.refs && info.refs[0] && info.refs[0].ext) {
                    o.ext = info.refs[0].ext;
                } else if (info.storage && info.storage.ext) {
                    o.ext = info.storage.ext.substring(1);
                }
                o.extMooshed = (o.ext ?? '').toLowerCase();
                if (o.extMooshed === 'jpeg') {
                    o.extMooshed = 'jpg';
                }

                // name
                if (info.file && info.file.name) {
                    o.name = info.file.name;
                } else {
                    o.name = `*.${o.ext}`;
                }

                // states
                if (info.file && info.file.state !== undefined) {
                    o.fileState = info.file.state;
                }
                if (info.refs && info.refs[0] && info.refs[0].state !== undefined) {
                    o.refsState = info.refs[0].state;
                }
                if (info.storage && info.storage.state !== undefined) {
                    o.storageState = info.storage.state;
                }

                // licenseeIds
                if (info.file && info.file.licenseeIds) {
                    info.file.licenseeIds.forEach((licenseeId: string) => {
                        o.licenseeIds.push(licenseeId);
                    });
                }
                if (info.storage && info.storage.metadata) {
                    info.storage.metadata.forEach((metadata: any) => {
                        const a = [] as string[];
                        if (metadata.licenseeIds) {
                            metadata.licenseeIds.split(',').forEach((licenseeId: string) => {
                                a.push(licenseeId);
                            });
                        }
                        if (o.licenseeIds.length !== a.length) {
                            if (info.file && info.file.state !== 0) {
                                o.problems.badLicenseeIds = true;
                            }
                        }
                        a.forEach((licenseeId) => {
                            if (!o.licenseeIds.includes(licenseeId)) {
                                if (info.file && info.file.state !== 0) {
                                    o.problems.badLicenseeIds = true;
                                }
                                o.licenseeIds.push(licenseeId);
                            }
                        });
                    });
                }

                // sizes
                if (info.storage && info.storage.sizes) {
                    o.sizes = info.storage.sizes;
                }

                // numRefs
                o.numRefs = 0;
                if (info.refs) {
                    o.numRefs = info.refs.length;
                }

                if (!info.refs) {
                    o.problems.notUsed = true;
                } else if (!info.storage) {
                    if (info.file && info.file.state === 0) {
                        o.problems.trappedOnDevice = true;
                    } else {
                        o.problems.lostForever = true;
                    }
                } else {
                    if (info.file) {
                        if (info.storage.state > info.file.state) {
                            o.problems.storageStateAhead = true;
                        } else if (info.storage.state < info.file.state) {
                            o.problems.storageStateBehind = true;
                        }
                    } else {
                        o.problems.storageWithNoFile = true;
                    }
                    if (info.storage.state > info.refs[0].state) {
                        o.problems.storageStateAhead = true;
                    } else if (info.storage.state < info.refs[0].state) {
                        o.problems.storageStateBehind = true;
                    }
                    info.refs.forEach((ref: any, index: number) => {
                        if (ref.state === undefined) {
                            o.problems.undefinedRefState = true;
                        } else if (index > 0 && ref.state !== info.refs[0].state) {
                            o.problems.inconsistentRefStates = true;
                        }
                    });
                    if (!o.problems.storageStateAhead && !o.problems.storageStateBehind && !o.problems.undefinedRefState) {
                        if (info.file && info.file.state !== info.refs[0].state) {
                            o.problems.badStatePropagation = true;
                        } else if (
                            info.file &&
                            info.file.state &&
                            info.file.state === 1 &&
                            o.extMooshed && (
                                isImage(o.extMooshed) ||
                                o.extMooshed === 'sfdoc'
                            )
                        ) {
                            o.problems.failedToOptimise = true;
                        }
                    }
                }

                if (Object.keys(o.problems).length === 0) {
                    delete o.problems;
                }

                currentFileInfoData.items.push(o);
                itemCount++;
            });

            return processTasks(
                fileInfoData,
                (data) => {
                    return protectFirestoreOperation(
                        `Add _fileInfoData`,
                        () => firestore.collection('_fileInfoData').add(data)
                    );
                }, {
                    batchSize: 50,
                    logProgressEvery: 100,
                    itemsName: 'create _fileInfoData tasks'
                }
            );

        }).then(() => {

            results.index = index;
            results.runningCount = runningCount + count;
            results.countFileDocs = results.runningCount;
            if (index < idCharacters.length) {
                results.notCompleted = true;
            }
            console.log(`Loaded count=${count} runningCount=${results.runningCount} and got up to index=${index} (${idCharacters[index]})`);
            return Promise.resolve();

        });
    });
};
