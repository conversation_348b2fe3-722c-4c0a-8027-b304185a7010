

//

import { DocumentData, DocumentSnapshot, FieldValue } from "firebase-admin/firestore";
import { firestore } from "../init";
import { licenseeCollectionsToDataSync, onCollectionUpdated, protectFirestoreOperation, vesselCollectionsToDataSync } from "../lib/util_firebase";
import { fileCollections } from "./filesConfig";

// justCheck: means we want to check that the existing state is what we expect (toState)
export const propagateFileState = (
    file: any,
    toState: number,
    justCheck: boolean,
    newFileExt?: string // Specify this to also propagate a new file extension
) => {
    const fileRefs = [...file.refs];
    const promises = [] as Promise<void>[];
    fileRefs.forEach((ref) => {
        promises.push(
            propagateFileStateForRef(
                file.id,
                ref.collection,
                ref.docId,
                toState,
                justCheck,
                newFileExt
            )
        );
    });
    if (justCheck) { // Just check the existing state = toState
        return Promise.all(promises);
    }
    return Promise.all(promises).then(() => {
        console.log(`propagateFileState toState=${toState} file`, file);
        return protectFirestoreOperation(
            `set state=${toState} for file ${file.id}`,
            () => firestore.collection('files').doc(file.id).set({
                state: toState
            }, {merge: true})
        );
    }).then(() => {
        console.log(`Completed file state propagation! id=${file.id} ${file.state} --> ${toState}`);
        return Promise.resolve();
    });
};
const propagateFileStateForRef = (
    fileId: string,
    collection: string,
    docId: string,
    toState: number,
    justCheck: boolean,
    newFileExt?: string
) => {
    if (!justCheck) {
        console.log(`propagateFileStateForRef fileId=${fileId} collection=${collection} docId=${docId} toState=${toState}`);
    }
    let hasModifiedData = false;
    let document: any;

    const updateFileField = (originalData: any, modifiedData: any, field: string, fileId: string, toState: number) => {
        const valueNeedsUpdating = (value: any) => {
            return (
                value &&
                typeof value === 'string' &&
                value.length > 20 &&
                value.substring(1,21) === fileId &&
                toState != parseInt(value.substring(0, 1)) // State must be different than existing state to bother updating
            );
        };
        const updateValue = (value: string) => {
            if (newFileExt) {
                return `${toState}${value.substring(1, value.lastIndexOf('.'))}.${newFileExt}`;
            }
            return `${toState}${value.substring(1)}`;
        };
        if (originalData && originalData[field]) {
            //if (typeof originalData[field] === 'object') {
            if (field === 'sfdoc') {
                const newObject = {
                    ...originalData[field]
                };
                let somethingChanged = false;
                Object.keys(originalData[field]).forEach((key) => {
                    if (valueNeedsUpdating(newObject[key])) {
                        somethingChanged = true;
                        newObject[key] = updateValue(newObject[key]);
                    }
                });
                if (somethingChanged) {
                    hasModifiedData = true;
                    modifiedData[field] = newObject;
                }
            } else if (Array.isArray(originalData[field])) {
                // Dealing with files array
                const newArray = [...originalData[field]];
                let somethingChanged = false;
                for (let i = 0; i < newArray.length; i++) {
                    if (valueNeedsUpdating(newArray[i])) {
                        somethingChanged = true;
                        newArray[i] = updateValue(newArray[i]);
                    }
                }
                if (somethingChanged) {
                    hasModifiedData = true;
                    modifiedData[field] = newArray;
                }
            } else {
                // Dealing with single file (signature)
                if (valueNeedsUpdating(originalData[field])) {
                    hasModifiedData = true;
                    modifiedData[field] = updateValue(originalData[field]);
                }
            }
        }
    };

    return firestore.runTransaction((transaction) => {
        // Update referenced doc if necessary
        const docRef = firestore.collection(collection).doc(docId);
        return transaction.get(docRef).then((doc) => {
            if (!doc.exists) {
                console.log(`Ref not found! collection=${collection} docId=${docId}`);
                return Promise.resolve();
            }
            document = {
                id: doc.id,
                ...doc.data()
            } as any;
            const modifiedData = {} as any;
            if (!justCheck) {
                console.log(`Updating ${collection}.${docId} document`, document);
            }
            if (collection === 'customFormVersions') {
                if (document.form) {
                    Object.keys(document.form).forEach((key) => {
                        const element = document.form[key];
                        if (element.help && element.help.files && element.help.files.length > 0) {
                            if (modifiedData.form === undefined) {
                                modifiedData.form = {};
                            }
                            if (modifiedData.form[key] === undefined) {
                                modifiedData.form[key] = {};
                            }
                            if (modifiedData.form[key].help === undefined) {
                                modifiedData.form[key].help = {
                                    files: element.help.files
                                };
                            }
                            updateFileField(element.help, modifiedData.form[key].help, 'files', fileId, toState);
                        }
                    });
                }
            } else if (collection === 'customFormsCompleted') {
                // Quick'n'dirty: just replace data.e<N> values that match
                if (document.data) {
                    modifiedData.data = {};
                    Object.keys(document.data).forEach((key) => {
                        updateFileField(document.data, modifiedData.data, key, fileId, toState);
                    });
                    if (Object.keys(modifiedData.data).length === 0) { // Nothing matched
                        delete modifiedData.data;
                    }
                }
            } else {
                if (!(fileCollections as any)[collection]) {
                    console.error(`>>> No config found for collections[${collection}]!`);
                }
                (fileCollections as any)[collection].forEach((field: string) => {
                    //console.log(`updateFileField ${fileId} field=${field} document`, document);
                    updateFileField(document, modifiedData, field, fileId, toState);
                    //console.log(`updateFileField ${fileId} field=${field} hasModifiedData=${hasModifiedData} modifiedData`, modifiedData);
                });
            }
            console.log(`${collection}.${docId} modifiedData`, modifiedData);
            hasModifiedData = Object.keys(modifiedData).length > 0;
            if (hasModifiedData) {
                if (
                    licenseeCollectionsToDataSync.includes(collection) ||
                    vesselCollectionsToDataSync.includes(collection)
                ) {
                    modifiedData.touched = FieldValue.serverTimestamp(); // touch referenced document
                }
                transaction.set(docRef, modifiedData, {merge: true});
                if (justCheck) {
                    console.log(`Fixed mismatch! fileId=${fileId} collection=${collection} docId=${docId} properState=${toState}`);
                    // console.log(`Fix mismatch! ${fileId} document`, document);
                    // console.log(`Fix mismatch! ${fileId} modifiedData`, modifiedData);
                }
            }
            return Promise.resolve();
        });
    }).then(() => {
        if (justCheck) {
            return Promise.resolve();
        }
        // Update whenVesselTouched or whenLicenseeTouched if necessary
        if (hasModifiedData) {
            if (licenseeCollectionsToDataSync.includes(collection)) {
                // Update whenLicenseeTouched if need be
                return Promise.resolve().then(() => {
                    if (document.licenseeId) {
                        return document.licenseeId;
                    } else if (collection === 'companyPlans') {
                        return document.id;
                    } else {
                        let vesselId = undefined;
                        if (document.vesselId) {
                            vesselId = document.vesselId;
                        } else if (document.vesselIds) {
                            vesselId = document.vesselIds[0];
                        }
                        if (vesselId) {
                            return protectFirestoreOperation(
                                `get vessel ${vesselId}`,
                                () => firestore.collection('vessels').doc(vesselId).get()
                            ).then((doc: DocumentSnapshot<DocumentData, DocumentData>) => {
                                return doc.data()!.licenseeId;
                            });
                        }
                        return Promise.reject(`Could not determine licenseeId`);
                    }
                }).then((licenseeId): Promise<any> => {
                    console.log(`whenLicenseeTouched --> onCollectionUpdated collection=${collection} licenseeId=${licenseeId} vesselId=${undefined} now=${Date.now()}`);
                    return onCollectionUpdated(collection, licenseeId, undefined);
                }).catch((error) => {
                    console.error(`Failed to to update whenLicenseeTouched collection=${collection} id=${document.id}`, error);
                });
            } else if (vesselCollectionsToDataSync.includes(collection)) {
                // Update whenVesselTouched if need be
                if (document.vesselId === undefined && document.vesselIds === undefined) {
                    console.error(`${collection}.${docId} is missing vesselId or vesselIds`, document);
                    return Promise.resolve();
                }
                let vesselIds = [] as string[];
                if (document.vesselIds) {
                    vesselIds = document.vesselIds;
                } else if (document.vesselId) {
                    vesselIds = [document.vesselId];
                }
                const processVesselIds = (): Promise<void> => {
                    if (vesselIds.length === 0) {
                        return Promise.resolve();
                    }
                    const vesselId = vesselIds.shift()!;
                    return Promise.resolve().then(() => {
                        // Obtain licenseeId
                        if (document.licenseeId) {
                            return document.licenseeId;
                        }
                        return protectFirestoreOperation(
                            `get vessel ${vesselId}`,
                            () => firestore.collection('vessels').doc(vesselId).get()
                        ).then((doc) => {
                            return doc.data().licenseeId;
                        });
                    }).then((licenseeId) => {
                        console.log(`whenVesselTouched --> onCollectionUpdated collection=${collection} licenseeId=${licenseeId} vesselId=${vesselId}`);
                        return onCollectionUpdated(collection, licenseeId, vesselId).catch((error) => {
                            console.error(`Failed to to update whenVesselTouched collection=${collection} id=${document.id} vesselId=${vesselId} licenseeId=${licenseeId}`, error);
                        });
                    }).then(() => {
                        return processVesselIds();
                    });
                };
                return processVesselIds();
            }
        }
        return Promise.resolve();
    });
};
