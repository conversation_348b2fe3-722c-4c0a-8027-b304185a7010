import { DocumentData, DocumentSnapshot, FieldValue, QuerySnapshot } from "firebase-admin/firestore";
import { firestore } from "../init";
import { processTasks, protectFirestoreOperation, requireSuperAdmin } from "../lib/util_firebase";
import { defaultEventHandlerOptionsForHttpsMaxResources, defaultEventHandlerOptionsForHttps, maxTimeoutSeconds } from "../eventHandlerOptions";
import { HttpsError, onCall } from "firebase-functions/https";
import { getStorage } from "firebase-admin/storage";
import { onDocumentCreated } from "firebase-functions/firestore";
import { formatTimeDuration } from "../lib/datesAndTime";


/**
 * Transfer a licensee to a different user within the same account.
 */
export const adminChangeLicensee = onCall({
        ...defaultEventHandlerOptionsForHttpsMaxResources
    }, (request, response) => {
        const data = request?.data;
        if (data === undefined) {
            throw new HttpsError('failed-precondition', 'Missing request data');
        }
        return requireSuperAdmin(request).then(() => {
            return changeLicensee(data);
        });
    }
);

/**
 * A lot can go wrong when changing licensees.
 * This attempts to find and fix any problems that may have been caused by adminChangeLicensee in the past.
 */
export const adminFixChangeLicenseeProblems = onCall({
    ...defaultEventHandlerOptionsForHttps
}, (request, response) => {
    return requireSuperAdmin(request).then(() => {
        console.log('adminFixChangeLicenseeProblems!');
        return protectFirestoreOperation(
            `Kick start _adminFixChangeLicenseeProblemsTrigger`,
            () => firestore.collection('_adminFixChangeLicenseeProblemsTrigger').add({
                whenStarted: Date.now(),
                index: 0,
                problems: {},
                totalProblems: 0
            })
        ).then(() => {
            return {message: 'success'};
        });
    });
});

/**
 * Allows adminFixChangeLicenseeProblems to run over multiple sequential function calls.
 */
export const adminFixChangeLicenseeProblemsTrigger = onDocumentCreated(
    {
        ...defaultEventHandlerOptionsForHttps,
        timeoutSeconds: maxTimeoutSeconds,
        document: '_adminFixChangeLicenseeProblemsTrigger/{id}'
    },
    (event) => {
        const data = {
            ...event.data!.data(),
            id: event.data!.id
        } as any;
        const whenStarted = data.whenStarted;
        console.log(`(---) adminFixChangeLicenseeProblemsTrigger! (${formatTimeDuration(Date.now() - whenStarted)} taken so far)`);
        return fixChangeLicenseeProblems(data).then(() => {
            data.index++;
            if (data.index >= licenseeIdCollections.length) {
                console.log(`(------------) Finished (Took ${formatTimeDuration(Date.now() - whenStarted)})`, data);
                return Promise.resolve();
            } else {
                return protectFirestoreOperation(
                    `fixChangeLicenseeProblemsTrigger add _adminFixChangeLicenseeProblemsTrigger index=${data.index}`,
                    () => firestore.collection('_adminFixChangeLicenseeProblemsTrigger').add(data)
                );
            }
        }).then(() => {
            return protectFirestoreOperation(
                `fixChangeLicenseeProblemsTrigger delete _adminFixChangeLicenseeProblemsTrigger snap.id=${data.id}`,
                () => firestore.collection('_adminFixChangeLicenseeProblemsTrigger').doc(data.id).delete()
            );
        });
    }
);

//
// This details all the ways licenseeId is used within various collections.
// It enables us to know how to change licensee and fix licensee problems.
//
// Each entry should specify either. (files is a special case that defined 2)
// docId: true
// field: 'licenseeId'
//
const licenseeIdCollections = [
    { collection: '_sendWeeklyReports', field: 'licenseeId' },
    { collection: '_unintentionalRefreshes', field: 'licenseeId' },
    { collection: 'actionLog', field: 'licenseeId' },
    { collection: 'actionsConfirmed', field: 'licenseeId' },
    { collection: 'companyDocumentCategories', field: 'licenseeId' },
    { collection: 'companyDocuments', field: 'licenseeId' },
    { collection: 'companyPlans', docId: true },
    { collection: 'contactCategories', field: 'licenseeId' },
    { collection: 'contacts', field: 'licenseeId' },
    { collection: 'correctiveActions', field: 'licenseeId' },
    { collection: 'crewCertificates', field: 'licenseeId' },
    { collection: 'crewCertificateTitles', field: 'licenseeId' },
    { collection: 'customFormCategories', field: 'licenseeId' },
    { collection: 'customForms', field: 'licenseeId' },
    { collection: 'customFormsCompleted', field: 'licenseeId' },
    { collection: 'customFormVersions', field: 'licenseeId' },
    { collection: 'errorReports', field: 'licenseeId' },
    //{ collection: 'files', field: 'licenseeId' }, // Now uses licenseeIds array
    { collection: 'incidentCategories', field: 'licenseeId' },
    { collection: 'incidentCauses', field: 'licenseeId' },
    { collection: 'incidentReviews', field: 'licenseeId' },
    { collection: 'incidents', field: 'licenseeId' },
    { collection: 'injuryLocations', field: 'licenseeId' },
    { collection: 'injuryTypes', field: 'licenseeId' },
    { collection: 'jobs', field: 'licenseeId' },
    { collection: 'licenseeSettings', docId: true },
    { collection: 'notifications', field: 'licenseeId' },
    { collection: 'overdueStats', docId: true },
    { collection: 'replicatedRisks', field: 'licenseeId' },
    { collection: 'riskCategories', field: 'licenseeId' },
    { collection: 'seaTimeRecords', field: 'licenseeId' },
    { collection: 'traceReports', field: 'licenseeId' },
    { collection: 'trainingTaskReports', field: 'licenseeId' },
    { collection: 'trainingTasks', field: 'licenseeId' },
    { collection: 'userDetails', field: 'licenseeId' },
    { collection: 'userDocuments', field: 'licenseeId' },
    { collection: 'userPermissionDefaults', field: 'licenseeId' },
    { collection: 'userRoles', field: 'licenseeId' },
    { collection: 'users', field: 'licenseeId' },
    { collection: 'vessels', field: 'licenseeId' },
    { collection: 'voyages', field: 'licenseeId' },
    { collection: 'whenLicenseeTouched', docId: true },
    { collection: 'whenVesselTouched', field: 'licenseeId' },
];

const changeLicensee = (data: any) => {
    console.log('changeLicensee! v16 data', data);

    let oldLicensee: any;
    let newLicensee: any;
    let oldLicenseeDetails: any;
    let newLicenseeDetails: any;
    let docsUpdated = 0;
    let filesUpdated = 0;
    const startTime = Date.now();

    const collections = [...licenseeIdCollections];
    const processCollections = (): Promise<void> => {
        if (collections.length === 0) {
            return Promise.resolve();
        }
        const config = collections.shift()!;

        return Promise.resolve().then(() => {
            if (config.docId) {
                return changeDocId(
                    config.collection,
                    oldLicensee.id,
                    newLicensee.id,
                    true // delete original document
                ).then(() => {
                    docsUpdated++;
                });
            }
            if (config.field) {
                return replaceAllFieldValues(
                    config.collection,
                    config.field,
                    oldLicensee.id,
                    newLicensee.id
                ).then((count) => {
                    docsUpdated += count;
                });
            }
            return Promise.resolve();
        }).then(() => {
            return processCollections();
        });
    };

    return Promise.resolve().then(() => {
        if (data?.userId) {
            return protectFirestoreOperation(
                `get newLicensee data.userId=${data.userId}`,
                () => firestore.collection('users').doc(data.userId).get()
            );
        } else {
            return Promise.reject('Invalid request');
        }
    }).then((doc: DocumentSnapshot<DocumentData, DocumentData>) => {
        newLicensee = {
            id: doc.id,
            ...doc.data()
        };
        console.log(`newLicensee id=${newLicensee.id} isLicensee=${newLicensee.isLicensee} licenseeId=${newLicensee.licenseeId}`, newLicensee);
        if (newLicensee.isLicensee) {
            return Promise.reject('User is already the licensee!');
        }
        return protectFirestoreOperation(
            `get oldLicensee newLicensee.licenseeId=${newLicensee.licenseeId}`,
            () => firestore.collection('users').doc(newLicensee.licenseeId).get()
        );
    }).then((doc) => {
        oldLicensee = {
            id: doc.id,
            ...doc.data()
        };
        console.log(`oldLicensee id=${oldLicensee.id} isLicensee=${oldLicensee.isLicensee} licenseeId=${oldLicensee.licenseeId}`, oldLicensee);
        return protectFirestoreOperation(
            `get newLicenseeDetails userDetails data.userId=${data.userId}`,
            () => firestore.collection('userDetails').doc(data.userId).get()
        );
    }).then((doc: DocumentSnapshot<DocumentData, DocumentData>) => {
        newLicenseeDetails = {
            id: doc.id,
            ...doc.data()
        };
        console.log('newLicenseeDetails', newLicenseeDetails);
        return protectFirestoreOperation(
            `get oldLicenseeDetails newLicensee.licenseeId=${newLicensee.licenseeId}`,
            () => firestore.collection('userDetails').doc(newLicensee.licenseeId).get()
        );
    }).then((doc: DocumentSnapshot<DocumentData, DocumentData>) => {
        oldLicenseeDetails = {
            id: doc.id,
            ...doc.data()
        };
        console.log('oldLicenseeDetails', oldLicenseeDetails);
        //return renameAllLicenseeFiles(oldLicensee.id, newLicensee.id, admin);
        return changeLicenseeIdForStorageObjects(oldLicensee.id, newLicensee.id);
    }).then((count) => {
        filesUpdated = count;
        console.log(`Updated ${filesUpdated} files (storage object metadata)`);

        // Update files.licenseeIds
        return replaceAllArrayFieldValues('files', 'licenseeIds', oldLicensee.id, newLicensee.id); // Will replace matched values within array
    }).then((count) => {
        docsUpdated += count;
        // Run through licenseeIdConfig
        return processCollections();
    }).then(() => {
        // copy userPermissions
        return changeDocId('userPermissions', oldLicensee.id, newLicensee.id, false);
    }).then(() => {
        docsUpdated++;
        // Copy the following fields and delete them afterwards (users)...
        // licenseeNumber
        console.log(`Updating newLicensee id=${newLicensee.id} with isLicensee: true, licenseeNumber: ${oldLicensee.licenseeNumber}, vesselIds=${oldLicensee.vesselIds}`);
        return protectFirestoreOperation(
            `changeLicensee update user newLicensee.id=${newLicensee.id}`,
            () => firestore.collection('users')
            .doc(newLicensee.id)
            .set({
                isLicensee: true,
                licenseeNumber: oldLicensee.licenseeNumber,
                vesselIds: oldLicensee.vesselIds
            }, { merge: true })
        ).then(() => {
            docsUpdated++;
            console.log(`Updated newLicensee`);
        });
    }).then(() => {
        console.log(`Updating oldLicensee id=${oldLicensee.id} with isLicensee: false, delete licenseeNumber...`);
        return protectFirestoreOperation(
            `changeLicensee update user oldLicensee.id=${oldLicensee.id}`,
            () => firestore.collection('users')
            .doc(oldLicensee.id)
            .set({
                isLicensee: false,
                licenseeNumber: FieldValue.delete()
            }, { merge: true })
        ).then(() => {
            docsUpdated++;
            console.log(`Updated oldLicensee`);
        });
    }).then(() => {
        console.log(`Updating licenseeSettings.previousLicenseeIds to include oldLicensee.id ${oldLicensee.id}`);
        return protectFirestoreOperation(
            `changeLicensee update licenseeSettings`,
            () => firestore.collection('licenseeSettings')
            .doc(newLicensee.id)
            .set({
                previousLicenseeIds: FieldValue.arrayUnion(oldLicensee.id)
            }, { merge: true })
        ).then(() => {
            docsUpdated++;
            console.log(`Updated licenseeSettings.previousLicenseeIds`);
        });
    }).then(() => {
        console.log(`Updating licenseeSettings.previousLicenseeIds to remove newLicensee.id ${newLicensee.id}`);
        return protectFirestoreOperation(
            `changeLicensee update licenseeSettings`,
            () => firestore.collection('licenseeSettings')
            .doc(newLicensee.id)
            .set({
                previousLicenseeIds: FieldValue.arrayRemove(newLicensee.id)
            }, { merge: true })
        ).then(() => {
            docsUpdated++;
            console.log(`Updated licenseeSettings.previousLicenseeIds`);
        });
    }).then(() => {
        // Copy the following fields and delete them afterwards (userDetails)...
        // companyName
        // companyAddress
        // stripeCreditCardDetails
        // numVessels
        return protectFirestoreOperation(
            `changeLicensee update userDetails newLicensee.id=${newLicensee.id}`,
            () => firestore.collection('userDetails')
            .doc(newLicensee.id)
            .set({
                companyName: oldLicenseeDetails.companyName,
                companyAddress: oldLicenseeDetails.companyAddress,
                stripeCreditCardDetails: oldLicenseeDetails.stripeCreditCardDetails,
                numVessels: oldLicenseeDetails.numVessels,
                touched: FieldValue.serverTimestamp()
            }, { merge: true })
        ).then(() => {
            docsUpdated++;
            console.log(`Updated userDetails for newLicensee.id=${newLicensee.id}`);
        });
    }).then(() => {
        return protectFirestoreOperation(
            `changeLicensee update userDetails oldLicensee.id=${oldLicensee.id}`,
            () => firestore.collection('userDetails')
            .doc(oldLicensee.id)
            .set({
                companyName: FieldValue.delete(),
                companyAddress: FieldValue.delete(),
                stripeCreditCardDetails: FieldValue.delete(),
                numVessels: FieldValue.delete(),
                touched: FieldValue.serverTimestamp()
            }, { merge: true })
        ).then(() => {
            docsUpdated++;
            console.log(`Updated userDetails for oldLicensee.id=${oldLicensee.id}`);
        });
    }).then(() => {
        console.log('Success, docsUpdated='+docsUpdated+', filesUpdated='+filesUpdated);
        const duration = Date.now() - startTime;
        console.log('Took '+(duration / 1000)+' seconds');
        console.log(`------------------- IMPORTANT! Please run adminDoMaintenance to fix any permission inconsistencies introduced due to onUsersWrite being flooded!`);
        return {
            result: 'success',
            message: `${newLicensee.firstName} ${newLicensee.lastName} ${newLicensee.id} is the new Licensee!`,
            docsUpdated: docsUpdated,
            filesUpdated: filesUpdated,
            timeTaken: duration
        };
    }).catch((error) => {
        console.log('got error', error);
        return {
            error: error
        };
    });
};



// Change a document's id
const changeDocId = (collection: string, docId: string, toDocId: string, deleteOriginal: boolean) => {
    console.log(`changeDocId collection=${collection} docId=${docId} toDocId=${toDocId} deleteOriginal=${deleteOriginal}`);
    // Get from document
    return protectFirestoreOperation(
        `changeDocId get from document collection=${collection} docId=${docId}`,
        () => firestore.collection(collection).doc(docId).get()
    ).then((doc: DocumentSnapshot<DocumentData, DocumentData>) => {
        if (doc.exists) {
            // Create document with toDocId
            return protectFirestoreOperation(
                `changeDocId create document collection=${collection} toDocId=${toDocId}`,
                () => firestore.collection(collection).doc(toDocId).set(doc.data()!)
            );
        }
        console.log(`changeDocId collection=${collection} docId=${docId} does not exist!!!`);
        return Promise.resolve();
    }).then(() => {
        if (deleteOriginal) {
            // Remove original document
            return protectFirestoreOperation(
                `changeDocId delete document collection=${collection} docId=${docId}`,
                () => firestore.collection(collection).doc(docId).delete()
            );
        }
        return Promise.resolve();
    });
};


// Replace all fields in collection with fromValue with toValue
// Resolves to count of number of changes made
const replaceAllFieldValues = (collection: string, field: string, fromValue: string, toValue: string) => {
    let count = 0;
    const docIds = [] as string[]; // list of docIds we want to update
    const o = {} as any;
    o[field] = toValue;
    const processDocId = (docId: string) => {
        return protectFirestoreOperation(
            `replaceAllFieldValues update collection=${collection} docId=${docId}`,
            () => firestore.collection(collection).doc(docId).update(o)
        ).then(() => {
            //console.log(`replaceFieldValue Updated ${collection}.${field} ${fromValue} -> ${toValue} for id=${docId}`);
            count++;
            return Promise.resolve();
        });
    };
    const processDocIds = (): Promise<number> => {
        if (docIds.length === 0) {
            console.log(`Converted ${count} ${collection}.${field} values`);
            return Promise.resolve(count);
        }

        const promises = [] as Promise<any>[];
        for (let i = 0; i < 20; i++) { // do 20 requests in parallel at a time
            if (docIds.length <= 0) {
                break;
            }
            promises.push(
                processDocId(docIds.shift()!)
            );
        }
        return Promise.all(promises).then(() => {
            return processDocIds();
        });
    };

    return protectFirestoreOperation(
        `replaceAllFieldValues get collection=${collection} field=${field} fromValue=${fromValue}`,
        () => firestore.collection(collection).where(field, '==', fromValue).get()
    ).then((snap: QuerySnapshot<DocumentData, DocumentData>) => {
        snap.docs.forEach((doc) => {
            docIds.push(doc.id);
        });
        return processDocIds();
    });
};

const replaceAllArrayFieldValues = (collection: string, field: string, fromValue: string, toValue: string) => {
    let count = 0;
    const documents = [] as any[]; // list of documents we want to update

    const processDocument = (document: any) => {
        const o = {} as any;
        //o[field] = toValue;
        o[field] = document[field];
        for (let i = 0; i < o[field].length; i++) {
            if (o[field][i] === fromValue) {
                o[field][i] = toValue;
            }
        }

        return protectFirestoreOperation(
            `replaceAllArrayFieldValues update collection=${collection} document.id=${document.id}`,
            () => firestore.collection(collection).doc(document.id).update(o)
        ).then(() => {
            count++;
            return Promise.resolve();
        });
    };
    const processDocuments = (): Promise<number> => {
        if (documents.length === 0) {
            console.log(`Converted ${count} ${collection}.${field} array values`);
            return Promise.resolve(count);
        }
        const promises = [];
        for (let i = 0; i < 20; i++) { // do 20 requests in parallel at a time
            if (documents.length <= 0) {
                break;
            }
            promises.push(processDocument(documents.shift()));
        }
        return Promise.all(promises).then(() => {
            return processDocuments();
        });
    };

    return protectFirestoreOperation(
        `replaceAllArrayFieldValues get collection=${collection} field=${field} fromValue=${fromValue}`,
        () => firestore.collection(collection).where(field, 'array-contains', fromValue).get()
    ).then((snap: QuerySnapshot<DocumentData, DocumentData>) => {
        snap.docs.forEach((doc) => {
            documents.push({
                id: doc.id,
                ...doc.data()
            });
        });
        return processDocuments();
    });
};

const changeLicenseeIdForStorageObjects = (fromLicenseeId: string, toLicenseeId: string) => {
    let count = 0;
    const bucket = getStorage().bucket();
    const tasks = [] as any[];

    const processTasks = (): Promise<void> => {
        if (tasks.length === 0) {
            return Promise.resolve();
        }
        const promises = [];
        for (let i = 0; i < 25; i++) {
            if (tasks.length === 0) {
                break;
            }
            const task = tasks.shift();
            count++;
            console.log(`[${count}] ${task.path} newLicenseeIds=${task.newLicenseeIds}`);
            promises.push(
                bucket.file(task.path).setMetadata({
                    metadata: {
                        licenseeIds: task.newLicenseeIds
                    }
                }).catch((error) => {
                    console.error(`FAILED to setMetadata for task.path=${task.path}`, task);
                })
            );
        }
        return Promise.all(promises).then(() => {
            return processTasks();
        });
    };

    console.log(`Getting list of all files in bucket ${bucket.name}... (can take a long time!)`);
    return bucket.getFiles({
        prefix: 'files/'
    }).then(([files]) => {
        files.forEach((file) => {
            if (
                file?.metadata?.metadata?.licenseeIds &&
                (file.metadata.metadata.licenseeIds as string).indexOf(fromLicenseeId) !== -1
            ) {
                tasks.push({
                    path: file.name,
                    //newLicenseeIds: (file.metadata.metadata.licenseeIds as string).replaceAll(fromLicenseeId, toLicenseeId)
                    newLicenseeIds: (file.metadata.metadata.licenseeIds as string).replace(new RegExp(fromLicenseeId, 'g'), toLicenseeId)
                    // .replace(
                });
            }
        });
        return processTasks();
    }).then(() => {
        return Promise.resolve(count);
    });
};


const fixChangeLicenseeProblems = (results: any) => {
    const index = results.index as number;

    const licenseeIdsByUserId = {} as any;
    const config = licenseeIdCollections[index];

    console.log(`[${index}] *** ${config.collection}`);
    if (results.problems[config.collection] === undefined) {
        results.problems[config.collection] = 0;
    }

    return protectFirestoreOperation(
        'get users',
        () => firestore.collection('users').get()
    ).then((snap: QuerySnapshot<DocumentData, DocumentData>) => {
        snap.docs.forEach((doc) => {
            licenseeIdsByUserId[doc.id] = doc.data().licenseeId;
        });
        return protectFirestoreOperation(
            `get ${config.collection}`,
            () => {
                if (config.collection == 'actionsConfirmed') {
                    return firestore.collection(config.collection).where('whenAction', '>', Date.now() - (10 * 24 * 60 * 60 * 1000)).get(); // To stop too many causing us to run out of memory (they are going to be deleted later anyway)
                } else if (config.collection == 'traceReports') {
                    return firestore.collection(config.collection).where('whenTraced', '>', Date.now() - (10 * 24 * 60 * 60 * 1000)).get(); // To stop too many causing us to run out of memory (they are going to be deleted later anyway)
                }
                return firestore.collection(config.collection).get();
            }
        );
    }).then((snap: QuerySnapshot<DocumentData, DocumentData>) => {
        const docsToChangeId = [] as any[];
        const docsToChangeField = [] as any[];
        snap.docs.forEach((doc) => {
            if (
                config.docId &&
                licenseeIdsByUserId[doc.id] &&
                doc.id !== licenseeIdsByUserId[doc.id]
            ) {
                // document is pointing to the wrong licenseeId
                docsToChangeId.push({
                    docId: doc.id,
                    toDocId: licenseeIdsByUserId[doc.id]
                });
            }
            if (
                config.field &&
                licenseeIdsByUserId[doc.data()[config.field]] &&
                doc.data()[config.field] !== licenseeIdsByUserId[doc.data()[config.field]]
            ) {
                // document field is pointing to the wrong licenseeId
                docsToChangeField.push({
                    docId: doc.id,
                    data: {
                        [config.field]: licenseeIdsByUserId[doc.data()[config.field]]
                    }
                });
            }
        });
        
        return processTasks(
            docsToChangeId,
            (docToChangeId) => {
                return changeDocId(config.collection, docToChangeId.docId, docToChangeId.toDocId, true).then(() => {
                    results.problems[config.collection]++;
                });
            }, {
                logProgressEvery: 100,
                itemsName: config.collection
            }
        ).then(() => {
            return processTasks(
                docsToChangeField,
                (docToChangeField) => {
                    return protectFirestoreOperation(
                        `Update ${config.collection}`,
                        () => firestore.collection(config.collection).doc(docToChangeField.docId).set(docToChangeField.data, { merge: true })
                    ).then(() => {
                        results.problems[config.collection]++;
                    });
                }, {
                    logProgressEvery: 100,
                    itemsName: config.collection
                }
            );
        });

    }).then(() => {
        if (results.problems[config.collection]) {
            results.totalProblems += results.problems[config.collection];
            console.log(`[${index}] ${config.collection} problems fixed: ${results.problems[config.collection]}`);
        }
        return Promise.resolve();
    });

};
