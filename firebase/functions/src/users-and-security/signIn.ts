import { HttpsError, onCall } from "firebase-functions/https";
import { defaultEventHandlerOptionsForHttps } from "../eventHandlerOptions";
import { settings } from "../projectSettings";
import { auth, firestore } from "../init";
import { protectFirestoreOperation } from "../lib/util_firebase";
import { generateRandomString } from "../lib/util_common";
import { DocumentData, DocumentSnapshot, QuerySnapshot } from "firebase-admin/firestore";
import axios from "axios";
import { sendMail } from "../lib/email";

/**
 * Called by clients to log in.
 * If email & password is correct, and MFA conditions are satisfied,
 * creates and returns a custom token that includes the value sfok = true.
 * This is what allows ALL security in Sea Flux work!
 */
export const signIn = onCall({
    ...defaultEventHandlerOptionsForHttps,
    concurrency: 8,
    memory: '256MiB', // Might need to increase this when increasing concurrency above -> nope, it's fine
    timeoutSeconds: 5 * 60,
    minInstances: 1 // This function could is a good candidate to use minInstances as logging in is a common user experience and one in which we don't want cold starts to be experienced
}, (request, response) => {

    const data = request?.data;
    if (data === undefined) {
        throw new HttpsError('failed-precondition', 'Missing request data');
    }

    const signInUrl = `https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=${settings.firebaseApiKey}`;

    return axios.post(signInUrl, {
        email: data.email.trim().toLowerCase(),
        password: data.password,
        returnSecureToken: true,
    } as any).then((result) => {
        // Email and password ok
        // Now we need to handle MFA (if turned on for licensee)

        const uid = result.data.localId;
        // Find out if we require Multi-factor...
        return auth.getUser(uid)
        .then((userRecord) => {
            console.log('userRecord.customClaims', userRecord.customClaims);
            // If requires MFA, then could either:
            // * createCustomToken which includes need for email
            // * send back needEmail... client will then ask for code and send code + email + password on next request
            // Will need to create code and send to user
            if (
                userRecord?.customClaims !== undefined &&
                userRecord.customClaims.firestoreUserId
            ) {
                const customClaims = userRecord.customClaims;
                return Promise.resolve().then(() => {
                    // Require MFA?
                    if (customClaims.state === undefined) {
                        return Promise.reject({myError: 'BadAccount', reason: 'No customClaims.state'});
                    } else if (customClaims.state !== 'active') {
                        switch (customClaims.state) {
                            case 'archived':
                                return Promise.reject({myError: 'AccountArchived', reason: 'customClaims.state=archived'});
                            case 'deactivated':
                                return Promise.reject({myError: 'AccountDeactivated', reason: 'customClaims.state=deactivated'});
                            case 'deleted':
                                return Promise.reject({myError: 'AccountDeleted', reason: 'customClaims.state=deleted'});
                            default:
                                return Promise.reject({myError: 'BadAccount', reason: 'customClaims.state is not active'});
                        }
                    } else if (customClaims.isSuperAdmin) {
                        console.log('isSuperAdmin');
                        return Promise.resolve(); // superAdmin never uses MFA
                    } else if (customClaims.sfmfa === undefined) {
                        return Promise.reject({myError: 'BadAccount', reason: 'No customClaims.sfmfa'});
                    } else if (customClaims.isLoginDisabled) {
                        return Promise.reject({myError: 'LoginDisabled', reason: 'This account is currently disabled'});
                    }

                    if (customClaims.sfmfa) {
                        console.log('Require MFA email code');
                        if (data.passcode) { // Passcode has been submitted
                            // Check passcode
                            if (data.passcode.length !== 6) {
                                return Promise.reject({
                                    myError: 'PasscodeInvalid',
                                    reason: 'Passcode not the right length'
                                });
                            }
                            return protectFirestoreOperation(
                                'get mfaEmailPasscodes',
                                () => firestore.collection('mfaEmailPasscodes')
                                .doc(customClaims.firestoreUserId)
                                .get()
                            ).then((doc: DocumentSnapshot<DocumentData, DocumentData>) => {
                                if (!doc.exists) {
                                    return Promise.reject({
                                        myError: 'PasscodeInvalid',
                                        reason: 'No email passcode was ever sent'
                                    });
                                }
                                if (doc.data()?.passcode === data.passcode) {
                                    if (Date.now() > doc.data()!.whenExpires) {
                                        return Promise.reject({
                                            myError: 'PasscodeExpired',
                                            reason: 'Passcode used is expired'
                                        });
                                    } else {
                                        return Promise.resolve(); // Successfully used email passcode to sign in
                                    }
                                } else {
                                    return Promise.reject({
                                        myError: 'PasscodeInvalid',
                                        reason: 'Passcode does not match most recent passcode emailed'
                                    });
                                }
                            });
                        }

                        // Send email with code
                        const passcode = generateRandomString(6, true);
                        return protectFirestoreOperation(
                            'get a users',
                            () => firestore.collection('users').doc(customClaims.firestoreUserId).get()
                        ).then((doc: DocumentSnapshot<DocumentData, DocumentData>) => {
                            if (!doc.exists) {
                                return Promise.reject({myError: 'BadAccount', reason: 'user not found'});
                            }
                            const user = {
                                id: doc.id,
                                ...doc.data()
                            } as any;
                            const promises = [] as Promise<any>[];

                            let toList = [];
                            if (!settings.preventDirectEmails) {
                                toList.push(`${user.firstName} ${user.lastName} <${data.email.trim()}>`);
                            }
                            if (settings.emailCopyTo) {
                                toList = [...toList, ...settings.emailCopyTo];
                            }
                            toList?.forEach((to) => {
                                promises.push(
                                    sendMail({
                                        from: settings.fromSeaFluxEmail,
                                        to: to,
                                        subject: `Sea Flux Multi-Factor authentication code`,
                                        html: `
                                            <p>Hi ${user.firstName},</p>
                                            <p>Please use the following authentication code to validate your login.</p>
                                            <p><b style="font-size:150%;">${passcode}</b></p>
                                            <p>This authentication code expires in 1 hour, but you can generate another by logging in again.</p>
                                            <br/>
                                            <p>
                                                <b>What to do if you didn't request this email</b>
                                            </p>
                                            <p>
                                                This email is sent automatically when you log into your Sea Flux account. If you haven't logged in recently, someone else might be trying to access your account. Please contact us immediately so we can make sure your data is safe.
                                                <br/>
                                                <a href="tel:+***********">+***********</a> or <a href="tel:+***********">+***********</a> or <a href="mailto:<EMAIL>"><EMAIL></a>
                                            </p>
                                            <br/>
                                            <p>
                                                Kind regards,
                                                <br/>
                                                The Sea Flux team
                                            </p>
                                        `,
                                    })
                                )
                            });

                            return Promise.all(promises).catch((error: any) => {
                                return Promise.reject({myError: 'SendEmailFailed', reason: 'Failed to send passcode email'});
                            });
                        }).then(() => {
                            return protectFirestoreOperation(
                                'set mfaEmailPasscodes',
                                () => firestore.collection('mfaEmailPasscodes')
                                    .doc(customClaims.firestoreUserId)
                                    .set({
                                        passcode: passcode,
                                        whenExpires: Date.now() + (15 * 60 * 1000) // 15 minutes in the future
                                    })
                            );
                        }).then(() => {
                            // Reject login (until user has entered the required passcode)
                            return Promise.reject({myError: 'RequireEmailPasscode', reason: 'MFA account requires passcode'});
                        });
                    } else {
                        return Promise.resolve(); // MFA not required
                    }

                }).then(() => {
                    // To get the following to work, you need to add the "Service Account Token Creator" to the principal <NAME_EMAIL> within Google Cloud Console > IAM.
                    return auth.createCustomToken(
                        uid,
                        {
                            sfok: true // Something that security rules can inspect to see if we have custom token and not just standard email/password login
                            // ... Need to store when logged in here, or when expires ... actually we already get this for free with auth_time
                        }
                    );
                });
            } else {
                return Promise.reject({myError: 'BadAccount', reason: 'No customClaims.firestoreUserId'});
            }
        }).then((customToken) => {
            return Promise.resolve({
                token: customToken
            });
        });

    }).catch(error => {
        console.log('SignIn failed', error);
        if (error.code === 'auth/user-not-found') {
            return Promise.resolve({
                result: 'fail',
                error: 'NoAccount'
            });
        } else if (error.code === 'ERR_BAD_REQUEST') {
            // Login failed due to missing auth object or incorrect email and/or password
            // Find out if email was the cause
            return protectFirestoreOperation(
                'get userDetails from email',
                () => firestore.collection('userDetails').where('email', '==', data.email.trim().toLowerCase()).get()
            ).then((snap: QuerySnapshot<DocumentData, DocumentData>) => {
                if (snap.docs.length === 0) {
                    return Promise.resolve({
                        result: 'fail',
                        error: 'NoAccount'
                    });
                }
                let activeFound = false;

                const userIdsToCheck = [] as string[];
                snap.docs.forEach((doc) => {
                    if (doc.data().state === 'active') {
                        userIdsToCheck.push(doc.id);
                    }
                });

                // see if any active userDetails have a corresponding user with uid
                const processUserIdsToCheck = (): Promise<void> => {
                    if (userIdsToCheck.length === 0) {
                        return Promise.resolve();
                    }
                    const userIdToCheck = userIdsToCheck.shift()!;
                    return protectFirestoreOperation(
                        'get users for processUserIdsToCheck',
                        () => firestore.collection('users').doc(userIdToCheck).get()
                    ).then((doc: DocumentSnapshot<DocumentData, DocumentData>) => {
                        if (doc.exists && doc.data()?.uid) {
                            activeFound = true;
                            return Promise.resolve();
                        }
                        return processUserIdsToCheck();
                    });
                };

                return processUserIdsToCheck().then(() => {
                    return Promise.resolve({
                        result: 'fail',
                        error: activeFound ? "BadPassword" : "NoAccount"
                    });
                });

            });
        } else if (error.myError) {
            return Promise.resolve({
                result: 'fail',
                error: error.myError,
                reason: error.reason
            });
        } else {
            return Promise.resolve({
                result: 'fail',
                error: "Other"
            });
        }
    });

});

