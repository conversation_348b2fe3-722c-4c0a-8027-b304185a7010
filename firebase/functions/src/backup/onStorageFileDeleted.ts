import { settings } from "../projectSettings";
import { defaultEventHandlerOptions } from "../eventHandlerOptions";
import { onObjectDeleted, StorageObjectData } from "firebase-functions/storage";
import { getStorage } from "firebase-admin/storage";


/**
 * Used to delete storage objects from archive buckets (settings.storageArchiveBuckets)
 * that are no longer needed.
 */
export const onStorageFileDeleted = onObjectDeleted(
    {
        ...defaultEventHandlerOptions,
        retry: true, // Will infinitely retry! Make sure all ERRORs not worth retrying are not returned.
        bucket: settings.storageBucket,
    },
    (event) => {
        try {
            const object = event.data;
            return unbackupStorageObject(object).catch((e) => {
                console.error('Caught error', e);
                return Promise.resolve(); // (Don't want an infinite retry loop)
            });
        } catch (e) {
            console.error('Caught error', e);
            return Promise.resolve(); // (Don't want an infinite retry loop)
        }
    }
);

const unbackupStorageObject = (object: StorageObjectData) => {
    console.log('Propagating delete', object.name);

    const promises = [] as Promise<void>[];
    settings.storageArchiveBuckets.forEach((bucketName) => {
        const destBucket = getStorage().bucket(bucketName);
        promises.push(
            destBucket.file(object.name).delete().then(() => {
                console.log(`Successfully deleted ${object.name} within ${bucketName}`);
            }).catch((error) => {
                console.error('Failed to delete file! error', error);
                return Promise.reject(error);
            })
        );
    });

    return Promise.all(promises).then(() => {
        console.log('onStorageFileDeleted successful');
    });
};
