import { DocumentData, QuerySnapshot } from "firebase-admin/firestore";
import { firestore } from "../init";
import { processTasks, protectFirestoreOperation, requireSuperAdmin } from "../lib/util_firebase";
import { onSchedule } from "firebase-functions/scheduler";
import { defaultEventHandlerOptionsForScheduled } from "../eventHandlerOptions";
import { onCall } from "firebase-functions/https";
import { categories, CategoryCollection } from "./categories";

/**
 * Schedules maintainCategories to run daily. see: adminMaintainCategories
 */
export const scheduledMaintainCategories = onSchedule(
    {
        ...defaultEventHandlerOptionsForScheduled,
        schedule: '0 1 * * *', // Run at 1am each day (NZST)
        memory: '1GiB'
    },
    (event) => {
        const results = {} as any;
        return maintainCategories(results).then(() => {
            console.log('RESULTS', results);
        });
    }
);

/**
 * Finds and fix any category references that should've been replaced by new ones.
 * Note: replacedCategories in results doesn't matter... it doesn't indicate that anything was fixed.
 */
export const adminMaintainCategories = onCall({
    ...defaultEventHandlerOptionsForScheduled
}, (request, response) => {
    const results = {} as any;
    return requireSuperAdmin(request).then(() => {
        return maintainCategories(results);
    }).then(() => {
        console.log('RESULTS (only need to worry about results.incidentReviewsUpdated  and incidentsUpdated)', results);
        return results;
    });
});



/**
 * Find and fix any category references that should've been replaced by new ones
 * Note: replacedCategories in results doesn't matter... it doesn't indicate that anything was fixed.
 */
const maintainCategories = (results: any) => {
    results.replacedCategories = {} as any;
    results.updatedDocuments = {} as any;
    const categoryCollections = Object.keys(categories);

    const processCategories = (): Promise<void> => {
        if (categoryCollections.length === 0) {
            return Promise.resolve();
        }
        const categoryCollection = categoryCollections.shift()!;
        const category = categories[categoryCollection as CategoryCollection];
        console.log(`[] ${categoryCollection}`);

        const replacedCategories = [] as any[];
        const processRefs = (replacedCategory: any, refs: any[]): Promise<void> => {
            if (refs.length === 0) {
                return Promise.resolve();
            }

            const ref = refs.shift();
            if (ref.collection === 'incidents' || ref.collection === 'incidentReviews') {
                // incidents are complicated so let's do them separately
                return processRefs(replacedCategory, refs);
            }

            const documents = [] as any[];
            const processDocuments = (): Promise<void> => {
                if (documents.length === 0) {
                    return Promise.resolve();
                }
                const document = documents.shift();
                console.log(`Update ${ref.collection} ${document.id} ${ref.field} ${replacedCategory.id} --> ${replacedCategory.replacedBy}`);
                if (results.updatedDocuments[ref.collection] === undefined) {
                    results.updatedDocuments[ref.collection] = 0;
                }
                results.updatedDocuments[ref.collection]++;
                const obj = {} as any;
                obj[ref.field] = replacedCategory.replacedBy;
                return protectFirestoreOperation(
                    `onCategoriesWrite update ${ref.collection} ${document.id}`,
                    () => firestore.collection(ref.collection).doc(document.id).update(obj)
                ).then(() => {
                    return processDocuments();
                });
            };
            return protectFirestoreOperation(
                `onCategoriesWrite get ${ref.collection} where ${ref.field} == ${replacedCategory.id}`,
                () => firestore.collection(ref.collection).where(ref.field, '==', replacedCategory.id).get()
            ).catch((error) => {
                console.log(`Error occurred. ref`, ref);
                console.log(`Error occurred. replacedCategory`, replacedCategory);
                throw error;
            }).then((snap: QuerySnapshot<DocumentData, DocumentData>) => {
                snap.docs.forEach((doc) => {
                    documents.push({
                        id: doc.id,
                        ...doc.data()
                    });
                });
                return processDocuments().then(() => {
                    return processRefs(replacedCategory, refs);
                });
            });
        };
        const processReplacedCategories = (): Promise<void> => {
            if (replacedCategories.length === 0) {
                return Promise.resolve();
            }
            const replacedCategory = replacedCategories.shift();
            return processRefs(replacedCategory, [...category.refs]).then(() => {
                return processReplacedCategories();
            });
        };

        return protectFirestoreOperation(
            `onCategoriesWrite get ${categoryCollection} where state == replaced`,
            () => firestore.collection(categoryCollection).where('state', '==', 'replaced').get()
        ).then((snap: QuerySnapshot<DocumentData, DocumentData>) => {
            snap.docs.forEach((doc) => {
                //console.log(`> replacedCategory ${doc.id}`, doc.data());
                if (results.replacedCategories[categoryCollection] === undefined) {
                    results.replacedCategories[categoryCollection] = 0;
                }
                results.replacedCategories[categoryCollection]++;
                replacedCategories.push({
                    id: doc.id,
                    ...doc.data()
                });
            });
            return processReplacedCategories().then(() => {
                return processCategories();
            });
        });
    };

    return processCategories().then(() => {
        return maintainIncidents(results);
    });
};


const maintainIncidents = (results: any) => {
    const categoryCollections = ['incidentCategories', 'incidentCauses', 'injuryTypes', 'injuryLocations'];
    results.incidentReviewsUpdated = 0;
    results.incidentsUpdated = 0;

    const replacedBys = {} as any;

    return processTasks(
        categoryCollections,
        (categoryCollection) => {
            return firestore.collection(categoryCollection).where('replacedBy', '!=', null).get().then((snap) => {
                console.log(`Found ${snap.docs.length} ${categoryCollection} categories that have been replaced.`);
                snap.docs.forEach((doc) => {
                    replacedBys[doc.id] = doc.data().replacedBy;
                });
            });
        }
    ).then(() => {
        console.log(`replacedBys`, replacedBys);
        // Run through incidentReviews
        return firestore.collection('incidentReviews').get();
    }).then((snap: QuerySnapshot<DocumentData, DocumentData>) => {
        console.log(`Looking through ${snap.docs.length} incidentReviews...`);
        return processTasks(
            snap.docs,
            (doc) => {
                const data = {
                    causeIds: doc.data().causeIds
                } as any;
                let somethingChanged = false;
                doc.data().causeIds?.forEach((causeId: string, index: number) => {
                    if (replacedBys[causeId]) {
                        console.log(`>>> Found causeId=${causeId} maps onto ${replacedBys[causeId]} for incidentReview ${doc.id}`);
                        somethingChanged = true;
                        data.causeIds[index] = replacedBys[causeId];
                    }
                });
                if (somethingChanged) {
                    results.incidentReviewsUpdated++;
                    return firestore.collection('incidentReviews').doc(doc.id).set(data, { merge: true });
                }
                return Promise.resolve();
            }
        );
    }).then(() => {
        // Run through incidents
        return firestore.collection('incidents').get();
    }).then((snap) => {
        console.log(`Looking through ${snap.docs.length} incidents...`);
        return processTasks(
            snap.docs,
            (doc) => {
                const incident = doc.data();
                const changedData = {
                    causeIds: incident.causeIds,
                    injuries: incident.injuries
                } as any;
                let somethingChanged = false;
                if (incident.categoryId && replacedBys[incident.categoryId]) {
                    console.log(`>>> Found categoryId=${incident.categoryId} maps onto ${replacedBys[incident.categoryId]} for incident ${doc.id}`);
                    somethingChanged = true;
                    changedData.categoryId = replacedBys[incident.categoryId];
                }
                incident.causeIds?.forEach((causeId: string, index: number) => {
                    if (replacedBys[causeId]) {
                        console.log(`>>> Found causeId=${causeId} maps onto ${replacedBys[causeId]} for incident ${doc.id}`);
                        somethingChanged = true;
                        changedData.causeIds[index] = replacedBys[causeId];
                    }
                });
                incident.injuries?.forEach((injury: any, injuryIndex: number) => {
                    injury.typeIds?.forEach((typeId: string, typeIndex: number) => {
                        if (replacedBys[typeId]) {
                            console.log(`>>> Found typeId=${typeId} maps onto ${replacedBys[typeId]} for incident.injuries[${injuryIndex}] ${doc.id}`);
                            somethingChanged = true;
                            changedData.injuries[injuryIndex].typeIds[typeIndex] = replacedBys[typeId];
                        }
                    });
                    injury.locationIds?.forEach((locationId: string, locationIndex: number) => {
                        if (replacedBys[locationId]) {
                            console.log(`>>> Found locationId=${locationId} maps onto ${replacedBys[locationId]} for incident.injuries[${injuryIndex}] ${doc.id}`);
                            somethingChanged = true;
                            changedData.injuries[injuryIndex].locationIds[locationIndex] = replacedBys[locationId];
                        }
                    });
                });
                if (somethingChanged) {
                    results.incidentsUpdated++;
                    return firestore.collection('incidents').doc(doc.id).set(changedData, { merge: true });
                }
                return Promise.resolve();
            }
        );
    }).then(() => {
        return Promise.resolve();
    });
};


// export const removeLegacyCategories = (results: any) => {
//     const util = require('./common/util');
//     console.log('removeLegacyCategories!');
//     results.vesselsWithLegacyCategoriesRemoved = 0;
//     results.licenseeSettingsWithLegacyCategoriesRemoved = 0;
//     const vessels = [] as any[];
//     const processVessels = () => {
//         if (vessels.length === 0) {
//             return Promise.resolve();
//         }
//         const vessel = vessels.shift();
//         if (
//             vessel.possibleSafetyItems ||
//             vessel.possibleSystemItems ||
//             vessel.possibleLocations
//         ) {
//             return util.protectFirestoreOperation(
//                 `onCategoriesWrite update vessels ${vessel.id}`,
//                 () => firestore.collection('vessels').doc(vessel.id).set({
//                     possibleSafetyItems: FieldValue.delete(),
//                     possibleSystemItems: FieldValue.delete(),
//                     possibleLocations: FieldValue.delete()
//                 }, { merge: true })
//             ).then(() => {
//                 results.vesselsWithLegacyCategoriesRemoved++;
//                 return processVessels();
//             });
//         }
//         return processVessels();
//     };
//     const licenseeSettingsArray = [] as any[];
//     const processLicenseeSettingsArray = () => {
//         if (licenseeSettingsArray.length === 0) {
//             return Promise.resolve();
//         }
//         const licenseeSettings = licenseeSettingsArray.shift();
//         if (
//             licenseeSettings.possibleRiskCategories ||
//             licenseeSettings.possibleFormCategories
//         ) {
//             return util.protectFirestoreOperation(
//                 `onCategoriesWrite update licenseeSettings ${licenseeSettings.id}`,
//                 () => firestore.collection('licenseeSettings').doc(licenseeSettings.id).set({
//                     possibleRiskCategories: FieldValue.delete(),
//                     possibleFormCategories: FieldValue.delete()
//                 }, { merge: true })
//             ).then(() => {
//                 results.licenseeSettingsWithLegacyCategoriesRemoved++;
//                 return processLicenseeSettingsArray();
//             });
//         }
//         return processLicenseeSettingsArray();
//     };
//     return util.protectFirestoreOperation(
//         `onCategoriesWrite get vessels`,
//         () => firestore.collection('vessels').get()
//     ).then((snap: QuerySnapshot<DocumentData, DocumentData>) => {
//         snap.docs.forEach((doc) => {
//             vessels.push({
//                 id: doc.id,
//                 ...doc.data()
//             });
//         });
//         return processVessels();
//     }).then(() => {
//         return util.protectFirestoreOperation(`onCategoriesWrite get licenseeSettings`, () => firestore.collection('licenseeSettings').get());
//     }).then((snap: QuerySnapshot<DocumentData, DocumentData>) => {
//         snap.docs.forEach((doc) => {
//             licenseeSettingsArray.push({
//                 id: doc.id,
//                 ...doc.data()
//             });
//         });
//         return processLicenseeSettingsArray();
//     });
// };

