import { FieldValue } from "firebase-admin/firestore";
import { firestore } from "../init";
import { processTasks } from "../lib/util_firebase";
import { categories, CategoryCollection } from "./categories";


/**
 * There was a bug in the app that caused duplicate categories with the same name to be saved.
 * The backend then caused them to refer to each other as being replaced by each other.
 * This function fixes the bad data caused by that happening.
 */
export const fixReplacedBySelfBug = (results: any) => {
    const categoryNames = Object.keys(categories);
    results.categoriesReplacedBySelfFixed = 0;

    return processTasks(
        categoryNames,
        (categoryName) => {
            return firestore.collection(categoryName).get().then((snap) => {
                const items = [] as any[];
                const itemsById = {} as any;
                console.log(`fixReplacedBySelfBug categoryName=${categoryName} (${snap.docs.length})`);
                snap.docs.forEach((doc) => {
                    const item = {
                        id: doc.id,
                        ...doc.data()
                    };
                    items.push(item);
                    itemsById[doc.id] = item;
                });

                const idsToCheck = [] as string[];
                items.forEach((item) => {
                    if (
                        item.state === 'replaced' &&
                        item.replacedBy &&
                        itemsById[item.replacedBy] &&
                        itemsById[item.replacedBy].state === 'replaced' &&
                        itemsById[item.replacedBy].replacedBy === item.id
                    ) {
                        console.log(`*** items replaced by each other name=${item.name} id=${item.id} replacedBy=${item.replacedBy}!`);
                        idsToCheck.push(item.id);
                    }
                });

                return processTasks(
                    idsToCheck,
                    (categoryId) => {
                        let isUsed = false;
                        const promises = [] as Promise<void>[];
                        categories[categoryName as CategoryCollection].refs.forEach((ref) => {
                            const field = (ref.field.indexOf('[]') !== -1) ?
                                ref.field.substring(0, ref.field.indexOf('[]'))
                                :
                                ref.field;
                            promises.push(
                                firestore.collection(ref.collection).where(field, '==', categoryId).get().then((snap) => {
                                    snap.docs.forEach((doc) => {
                                        console.log(`!!! ${categoryName} ref ${ref.collection}.${ref.field} id=${categoryId}`);
                                        isUsed = true;
                                    });
                                })
                            );
                            promises.push(
                                firestore.collection(ref.collection).where(field, 'array-contains', categoryId).get().then((snap) => {
                                    if (snap.docs.length) {
                                        console.log(`!!!!!!!!! found ${snap.docs.length} refs! (array-contains)`);
                                        isUsed = true;
                                    }
                                })
                            );
                        });
                        return Promise.all(promises).then(() => {
                            if (isUsed) {
                                return firestore.collection(categoryName).doc(categoryId).set({
                                    state: 'active',
                                    replacedBy: FieldValue.delete()
                                }, { merge: true }).then(() => {
                                    console.log(`^^^ Activated ${categoryName} categoryId=${categoryId}`);
                                });
                            } else {
                                return firestore.collection(categoryName).doc(categoryId).delete().then(() => {
                                    console.log(`(x) Deleted ${categoryName} categoryId=${categoryId}`);
                                })
                            }
                        }).then(() => {
                            results.categoriesReplacedBySelfFixed++;
                        });
                    }
                );
            });
        }
    );
};
