import { DocumentData, FieldValue, QuerySnapshot } from "firebase-admin/firestore";
import { firestore } from "../init";
import { protectFirestoreOperation } from "../lib/util_firebase";


/**
 * Remove legacy category data like:
 * - vessel.possibleSafetyItems
 * - vessel.possibleSystemItems
 * - vessel.possibleLocations
 * - licenseeSettings.possibleRiskCategories
 * - licenseeSettings.possibleFormCategories
 */
export const removeLegacyCategories = (results: any) => {
    console.log('removeLegacyCategories!');
    results.vesselsWithLegacyCategoriesRemoved = 0;
    results.licenseeSettingsWithLegacyCategoriesRemoved = 0;
    const vessels = [] as any[];
    const processVessels = (): Promise<void> => {
        if (vessels.length === 0) {
            return Promise.resolve();
        }
        const vessel = vessels.shift();
        if (
            vessel.possibleSafetyItems ||
            vessel.possibleSystemItems ||
            vessel.possibleLocations
        ) {
            return protectFirestoreOperation(
                `onCategoriesWrite update vessels ${vessel.id}`,
                () => firestore.collection('vessels').doc(vessel.id).set({
                    possibleSafetyItems: FieldValue.delete(),
                    possibleSystemItems: FieldValue.delete(),
                    possibleLocations: FieldValue.delete()
                }, { merge: true })
            ).then(() => {
                results.vesselsWithLegacyCategoriesRemoved++;
                return processVessels();
            });
        }
        return processVessels();
    };
    const licenseeSettingsArray = [] as any[];
    const processLicenseeSettingsArray = (): Promise<void> => {
        if (licenseeSettingsArray.length === 0) {
            return Promise.resolve();
        }
        const licenseeSettings = licenseeSettingsArray.shift();
        if (
            licenseeSettings.possibleRiskCategories ||
            licenseeSettings.possibleFormCategories
        ) {
            return protectFirestoreOperation(
                `onCategoriesWrite update licenseeSettings ${licenseeSettings.id}`,
                () => firestore.collection('licenseeSettings').doc(licenseeSettings.id).set({
                    possibleRiskCategories: FieldValue.delete(),
                    possibleFormCategories: FieldValue.delete()
                }, { merge: true })
            ).then(() => {
                results.licenseeSettingsWithLegacyCategoriesRemoved++;
                return processLicenseeSettingsArray();
            });
        }
        return processLicenseeSettingsArray();
    };
    return protectFirestoreOperation(
        `onCategoriesWrite get vessels`,
        () => firestore.collection('vessels').get()
    ).then((snap: QuerySnapshot<DocumentData, DocumentData>) => {
        snap.docs.forEach((doc) => {
            vessels.push({
                id: doc.id,
                ...doc.data()
            });
        });
        return processVessels();
    }).then(() => {
        return protectFirestoreOperation(
            `onCategoriesWrite get licenseeSettings`,
            () => firestore.collection('licenseeSettings').get()
        );
    }).then((snap: QuerySnapshot<DocumentData, DocumentData>) => {
        snap.docs.forEach((doc) => {
            licenseeSettingsArray.push({
                id: doc.id,
                ...doc.data()
            });
        });
        return processLicenseeSettingsArray();
    });
};
