import { onDocumentUpdated } from "firebase-functions/firestore";
import { defaultEventHandlerOptions } from "../eventHandlerOptions";
import { FieldValue } from "firebase-admin/firestore";
import { firestore } from "../init";


/**
 * Propagate deleted state to dependants.
 */
export const onEquipmentUpdate = onDocumentUpdated(
    {
        ...defaultEventHandlerOptions,
        document: 'equipment/{id}',
    },
    (event) => {
        const change = event.data!;
        const equipment = {
            id: change.after.id,
            ...change.after.data(),
        } as any;
        return processChangedEquipment(equipment);
    }
);


const processChangedEquipment = (equipment: any) => {
    if (equipment.state === 'deleted') {
        console.log(`Propagating deletion of equipment ${equipment.id} ${equipment.equipment}...`);
        // Remove scheduledMaintenanceTasks that refer to deleted equipment
        return firestore.runTransaction((transaction) => {
            return transaction.get(
                firestore.collection('scheduledMaintenanceTasks')
                .where('equipmentId', '==', equipment.id)
                .where('state', '!=', 'deleted')
            ).then((snap) => {
                if (snap.docs.length > 0) {
                    snap.docs.forEach((doc) => {
                        console.log(`[${equipment.id}] Deleting scheduledMaintenanceTasks.id=${doc.id}`);
                        transaction.set(
                            firestore.collection('scheduledMaintenanceTasks').doc(doc.id),
                            {
                                state: 'deleted',
                                whenDeleted: equipment.whenDeleted,
                                deletedBy: equipment.deletedBy
                            },
                            { merge: true }
                        );
                    });
                    transaction.set( // whenVesselTouched
                        firestore.collection('whenVesselTouched').doc(equipment.vesselId),
                        {
                            scheduledMaintenanceTasks: FieldValue.serverTimestamp(),
                            touched: FieldValue.serverTimestamp()
                        },
                        { merge: true }
                    );
                }
            });
        }).then(() => {
            // Remove deleted equipment from any spareParts that refer to it
            return firestore.runTransaction((transaction) => {
                return transaction.get(
                    firestore.collection('spareParts')
                    .where('equipmentIds', 'array-contains', equipment.id)
                ).then((snap) => {
                    if (snap.docs.length > 0) {
                        snap.docs.forEach((doc) => {
                            console.log(`[${equipment.id}] Removing equipment from spareParts.id=${doc.id}`);
                            transaction.set(
                                firestore.collection('spareParts').doc(doc.id),
                                {
                                    equipmentIds: FieldValue.arrayRemove(equipment.id)
                                },
                                { merge: true }
                            );
                        });
                        transaction.set( // whenVesselTouched
                            firestore.collection('whenVesselTouched').doc(equipment.vesselId),
                            {
                                spareParts: FieldValue.serverTimestamp(),
                                touched: FieldValue.serverTimestamp()
                            },
                            { merge: true }
                        );
                    }
                });
            });
        });
    }
    return;
};