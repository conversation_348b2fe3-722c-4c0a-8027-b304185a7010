rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /files/{path=**} {
      //allow read, write: if true; //request.auth!=null;
      // We only want firebase functions to be able to update or delete files.
      allow read: if isAdmin() || (
        isValidUser() &&
        resource.metadata.licenseeIds.matches(".*"+request.auth.token.licenseeId+".*")
      ) || (
        resource.metadata.emailToken == request.params.emailToken
      ) || (
        isValidUser() &&
        resource.metadata.isShared
      );
      allow create: if isAdmin() || (
        isValidUser() && (
          request.resource.metadata.licenseeIds == request.auth.token.licenseeId
        )
      ); // must be logged in to upload files, and licenseeIds must match licenseeId
    }








    //
    // Functions
    //

    function isAdmin() {
      return request.auth != null && request.auth.token.isSuperAdmin;
    }
    function isValidUser() {
      return (
        request.auth != null &&
        request.auth.token.state == 'active' &&
        !("isDeactivated" in request.auth.token && request.auth.token.isDeactivated) &&
        !("isLoginDisabled" in request.auth.token && request.auth.token.isLoginDisabled) &&
        request.auth.token.sfok && // (means the correct custom token was created by signIn function)
        (
          request.auth.token.sfmss == 0 || ( // session timeout is unlimited
            request.time.toMillis() < (request.auth.token.auth_time + request.auth.token.sfmss) * 1000 // session not yet expired
          )
        )
      );
    }

  }
}
