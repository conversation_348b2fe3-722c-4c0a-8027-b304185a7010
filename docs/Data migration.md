# Migrating Data

Some releases involve changes to firestore data and backend structure.

Migration of data should be handled by the Firebase Function ```adminDoMigration```.
Alternatively, there could be some necessary data manipulation within ```adminDoMaintenance```.

In either case, if any changes involve changes userPermisions, then we'll want to conduct the migration at a time with low user engagement (such as Sunday evening NZST). This is because changing user permissions triggers change requests to Firebase Authentication to update security claims which can bottle neck due to the spike in requests. This is bad because users may experience "forever reloading" while their firestore permissions are not in sync with their authentication security claims.

Note: Running adminDoMaintenance is a good idea in this situation as it checks and makes sure user permissions are in sync with Firebase Authentication - just inc ase something went wrong.

